# ADC Restaurant Platform Makefile
# This Makefile provides commands for running the frontend and backend servers

# Variables
FRONTEND_PORT = 4000
BACKEND_PORT = 5000
DB_URL = $(shell grep DATABASE_URL .env 2>/dev/null | cut -d '=' -f2 || echo "postgresql://postgres:postgres@localhost:5432/adc_restaurant")

# Colors for terminal output
YELLOW = \033[1;33m
GREEN = \033[1;32m
RED = \033[1;31m
BLUE = \033[1;34m
NC = \033[0m # No Color

# Default target
.PHONY: all
all: help

# Help command
.PHONY: help
help:
	@echo "${BLUE}ADC Restaurant Platform Development Commands${NC}"
	@echo ""
	@echo "${YELLOW}Usage:${NC}"
	@echo "  make ${GREEN}<command>${NC}"
	@echo ""
	@echo "${YELLOW}Available commands:${NC}"
	@echo "  ${GREEN}dev${NC}              - Start both frontend and backend servers"
	@echo "  ${GREEN}frontend${NC}         - Start only the frontend server"
	@echo "  ${GREEN}backend${NC}          - Start only the backend server (placeholder for future Order Service API)"
	@echo "  ${GREEN}db-setup${NC}         - Set up the database (generate Prisma client)"
	@echo "  ${GREEN}db-migrate${NC}       - Run database migrations"
	@echo "  ${GREEN}db-seed${NC}          - Seed the database with initial data"
	@echo "  ${GREEN}db-studio${NC}        - Open Prisma Studio to view/edit database"
	@echo "  ${GREEN}clean${NC}            - Clean build artifacts"
	@echo "  ${GREEN}build${NC}            - Build the application for production"
	@echo "  ${GREEN}install${NC}          - Install dependencies"
	@echo "  ${GREEN}help${NC}             - Show this help message"
	@echo ""
	@echo "${YELLOW}Examples:${NC}"
	@echo "  make dev          # Start both servers"
	@echo "  make frontend     # Start only the frontend"
	@echo "  make db-setup     # Set up the database"

# Start both frontend and backend servers
.PHONY: dev
dev:
	@echo "${BLUE}Starting development servers...${NC}"
	@make -j 2 frontend backend

# Start frontend server
.PHONY: frontend
frontend:
	@echo "${GREEN}Starting frontend server on port ${FRONTEND_PORT}...${NC}"
	@bun run dev

# Start backend server (placeholder for future Order Service API)
.PHONY: backend
backend:
	@echo "${YELLOW}Note: The Order Service API is planned but not yet implemented.${NC}"
	@echo "${YELLOW}This is a placeholder for the future backend server.${NC}"
	@echo "${GREEN}When implemented, the backend server will run on port ${BACKEND_PORT}.${NC}"
	@# Uncomment the following line when the Order Service API is implemented
	@# cd order-service && bun run dev

# Database setup
.PHONY: db-setup
db-setup:
	@echo "${GREEN}Setting up database...${NC}"
	@npx prisma generate

# Database migrations
.PHONY: db-migrate
db-migrate:
	@echo "${GREEN}Running database migrations...${NC}"
	@npx prisma migrate dev

# Seed database
.PHONY: db-seed
db-seed:
	@echo "${GREEN}Seeding database...${NC}"
	@npx prisma db seed

# Open Prisma Studio
.PHONY: db-studio
db-studio:
	@echo "${GREEN}Opening Prisma Studio...${NC}"
	@npx prisma studio

# Clean build artifacts
.PHONY: clean
clean:
	@echo "${GREEN}Cleaning build artifacts...${NC}"
	@rm -rf .next
	@rm -rf node_modules/.cache

# Build for production
.PHONY: build
build:
	@echo "${GREEN}Building for production...${NC}"
	@bun run build

# Install dependencies
.PHONY: install
install:
	@echo "${GREEN}Installing dependencies...${NC}"
	@bun install

# Docker commands (for future use)
.PHONY: docker-up
docker-up:
	@echo "${GREEN}Starting Docker containers...${NC}"
	@docker-compose up -d

.PHONY: docker-down
docker-down:
	@echo "${GREEN}Stopping Docker containers...${NC}"
	@docker-compose down

# Check environment
.PHONY: check-env
check-env:
	@echo "${GREEN}Checking environment...${NC}"
	@if [ ! -f .env ]; then \
		echo "${RED}Error: .env file not found. Please create one based on .env.example${NC}"; \
		exit 1; \
	fi
	@echo "${GREEN}Environment check passed.${NC}"
