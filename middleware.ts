import createMiddleware from 'next-intl/middleware';
import { locales, defaultLocale } from './src/i18n/config';

export default createMiddleware({
  // A list of all locales that are supported
  locales,

  // Used when no locale matches
  defaultLocale,

  // If this locale is matched, pathnames work without a prefix (e.g. `/about`)
  localePrefix: 'as-needed'
});

export const config = {
  // Skip all paths that should not be internationalized
  // This includes api routes, static files, and other internal routes
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)']
};
