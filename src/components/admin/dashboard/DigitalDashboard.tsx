'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/merchantApi';
import { 
  useGetDigitalProductsQuery, 
  useGetDownloadStatsQuery,
  useGetLicensesQuery
} from '@/lib/redux/api/endpoints/digitalApi';
import { useState } from 'react';
import Link from 'next/link';

interface DigitalDashboardProps {
  merchantId: string;
}

export default function DigitalDashboard({ merchantId }: DigitalDashboardProps) {
  const { data: merchant, isLoading: merchantLoading } = useGetMerchantsQuery(undefined, {
    selectFromResult: (result) => ({
      ...result,
      data: result.data?.find(m => m.id === merchantId && m.type === 'digital')
    })
  });
  
  const { data: products, isLoading: productsLoading } = useGetDigitalProductsQuery(merchantId);
  const { data: downloadStats, isLoading: statsLoading } = useGetDownloadStatsQuery(merchantId);
  const { data: licenses, isLoading: licensesLoading } = useGetLicensesQuery({ merchantId });
  
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'licenses' | 'downloads'>('overview');
  
  if (merchantLoading || productsLoading || statsLoading || licensesLoading) {
    return <div className="p-4">Loading digital store data...</div>;
  }
  
  if (!merchant) {
    return <div className="p-4">Digital store not found or not a digital type merchant.</div>;
  }
  
  // Filter active licenses
  const activeLicenses = licenses?.filter(license => license.status === 'active') || [];
  
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-6 border-b">
        <div className="flex items-center">
          {merchant.logo && (
            <img
              src={merchant.logo}
              alt={merchant.name}
              className="h-16 w-16 rounded-full mr-4 object-cover"
            />
          )}
          <div>
            <h1 className="text-2xl font-bold">{merchant.name}</h1>
            <p className="text-gray-600">{merchant.address}</p>
          </div>
        </div>
      </div>
      
      <div className="border-b">
        <nav className="flex">
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'products' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('products')}
          >
            Products
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'licenses' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('licenses')}
          >
            Licenses
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'downloads' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('downloads')}
          >
            Downloads
          </button>
        </nav>
      </div>
      
      <div className="p-6">
        {activeTab === 'overview' && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Digital Store Overview</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Digital Products</h3>
                <p className="text-2xl font-bold">{products?.length || 0}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Active Licenses</h3>
                <p className="text-2xl font-bold">{activeLicenses.length}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Today's Downloads</h3>
                <p className="text-2xl font-bold">{downloadStats?.today || 0}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Total Downloads</h3>
                <p className="text-2xl font-bold">{downloadStats?.total || 0}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <h3 className="font-medium mb-2">Store Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Automatic Delivery</h4>
                  <p>{merchant.settings?.automaticDelivery ? 'Enabled' : 'Disabled'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Download Limit</h4>
                  <p>{merchant.settings?.downloadLimitPerPurchase || 'Unlimited'} per purchase</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Download Expiry</h4>
                  <p>{merchant.settings?.downloadExpiryDays ? `${merchant.settings.downloadExpiryDays} days` : 'Never'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Watermark</h4>
                  <p>{merchant.settings?.watermarkEnabled ? 'Enabled' : 'Disabled'}</p>
                </div>
              </div>
            </div>
            
            <div className="mb-6">
              <h3 className="font-medium mb-2">Download Statistics</h3>
              <div className="p-4 border rounded-md">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Today</h4>
                    <p className="text-xl font-bold">{downloadStats?.today || 0}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">This Week</h4>
                    <p className="text-xl font-bold">{downloadStats?.thisWeek || 0}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">This Month</h4>
                    <p className="text-xl font-bold">{downloadStats?.thisMonth || 0}</p>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <div className="flex justify-between items-center">
                    <h4 className="text-sm font-medium text-gray-500">Total Downloads</h4>
                    <p className="text-xl font-bold">{downloadStats?.total || 0}</p>
                  </div>
                </div>
              </div>
            </div>
            
            {products && products.length > 0 && (
              <div className="mb-6">
                <h3 className="font-medium mb-2">Top Products</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Price
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Downloads
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {products.slice(0, 5).map((product) => (
                        <tr key={product.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {product.images?.[0] && (
                                <img
                                  src={product.images[0]}
                                  alt={product.name}
                                  className="h-10 w-10 rounded-md mr-3 object-cover"
                                />
                              )}
                              <div className="font-medium text-gray-900">{product.name}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {product.salePrice ? (
                              <div>
                                <span className="text-red-600 font-medium">${product.salePrice.toFixed(2)}</span>
                                <span className="text-gray-500 line-through ml-2">${product.price.toFixed(2)}</span>
                              </div>
                            ) : (
                              <span className="text-gray-900">${product.price.toFixed(2)}</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {/* This would come from download stats per product */}
                            0
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link 
                              href={`/admin/merchants/digital/${merchantId}/products/${product.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              View Details
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {products.length > 5 && (
                    <div className="px-6 py-3 bg-gray-50 text-right">
                      <Link 
                        href={`/admin/merchants/digital/${merchantId}/products`}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        View all products →
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'products' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Digital Products</h2>
              <Link
                href={`/admin/merchants/digital/${merchantId}/products/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Add Product
              </Link>
            </div>
            
            {products && products.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        License
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map((product) => (
                      <tr key={product.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {product.images?.[0] && (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="h-10 w-10 rounded-md mr-3 object-cover"
                              />
                            )}
                            <div>
                              <div className="font-medium text-gray-900">{product.name}</div>
                              <div className="text-sm text-gray-500">{product.version}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {product.fileType}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {product.salePrice ? (
                            <div>
                              <span className="text-red-600 font-medium">${product.salePrice.toFixed(2)}</span>
                              <span className="text-gray-500 line-through ml-2">${product.price.toFixed(2)}</span>
                            </div>
                          ) : (
                            <span className="text-gray-900">${product.price.toFixed(2)}</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {product.licenseType}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            product.available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.available ? 'Available' : 'Unavailable'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link 
                            href={`/admin/merchants/digital/${merchantId}/products/${product.id}`}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            View
                          </Link>
                          <Link 
                            href={`/admin/merchants/digital/${merchantId}/products/${product.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            Edit
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No digital products found. Add your first product to get started.</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'licenses' && (
          <div className="text-center py-8 text-gray-500">
            <p>License management coming soon.</p>
          </div>
        )}
        
        {activeTab === 'downloads' && (
          <div className="text-center py-8 text-gray-500">
            <p>Download statistics coming soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
