'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/merchantApi';
import { useGetProductsQuery } from '@/lib/redux/api/endpoints/retailApi';
import { useState } from 'react';
import Link from 'next/link';

interface RetailDashboardProps {
  merchantId: string;
}

export default function RetailDashboard({ merchantId }: RetailDashboardProps) {
  const { data: merchant, isLoading: merchantLoading } = useGetMerchantsQuery(undefined, {
    selectFromResult: (result) => ({
      ...result,
      data: result.data?.find(m => m.id === merchantId && m.type === 'retail')
    })
  });
  
  const { data: products, isLoading: productsLoading } = useGetProductsQuery(merchantId);
  
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'inventory' | 'orders'>('overview');
  
  if (merchantLoading || productsLoading) {
    return <div className="p-4">Loading retail data...</div>;
  }
  
  if (!merchant) {
    return <div className="p-4">Retail store not found or not a retail type merchant.</div>;
  }
  
  // Calculate inventory stats
  const lowStockProducts = products?.filter(p => p.inventoryCount <= p.inventoryThreshold) || [];
  const outOfStockProducts = products?.filter(p => p.inventoryCount === 0) || [];
  
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-6 border-b">
        <div className="flex items-center">
          {merchant.logo && (
            <img
              src={merchant.logo}
              alt={merchant.name}
              className="h-16 w-16 rounded-full mr-4 object-cover"
            />
          )}
          <div>
            <h1 className="text-2xl font-bold">{merchant.name}</h1>
            <p className="text-gray-600">{merchant.address}</p>
          </div>
        </div>
      </div>
      
      <div className="border-b">
        <nav className="flex">
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'products' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('products')}
          >
            Products
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'inventory' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('inventory')}
          >
            Inventory
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'orders' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('orders')}
          >
            Orders
          </button>
        </nav>
      </div>
      
      <div className="p-6">
        {activeTab === 'overview' && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Retail Store Overview</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Total Products</h3>
                <p className="text-2xl font-bold">{products?.length || 0}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Low Stock Items</h3>
                <p className="text-2xl font-bold text-yellow-600">{lowStockProducts.length}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Out of Stock</h3>
                <p className="text-2xl font-bold text-red-600">{outOfStockProducts.length}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <h3 className="font-medium mb-2">Store Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Inventory Management</h4>
                  <p>{merchant.settings?.inventoryManagement ? 'Enabled' : 'Disabled'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Allow Backorders</h4>
                  <p>{merchant.settings?.allowBackorders ? 'Yes' : 'No'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Shipping Methods</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {merchant.settings?.shippingMethods?.map((method, index) => (
                      <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        {method}
                      </span>
                    ))}
                  </div>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Return Policy</h4>
                  <p className="text-sm">{merchant.settings?.returnPolicy || 'Not specified'}</p>
                </div>
              </div>
            </div>
            
            {lowStockProducts.length > 0 && (
              <div className="mb-6">
                <h3 className="font-medium mb-2">Low Stock Alerts</h3>
                <div className="border rounded-md overflow-hidden">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Product
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Current Stock
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Threshold
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Action
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {lowStockProducts.slice(0, 5).map((product) => (
                        <tr key={product.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {product.images?.[0] && (
                                <img
                                  src={product.images[0]}
                                  alt={product.name}
                                  className="h-10 w-10 rounded-md mr-3 object-cover"
                                />
                              )}
                              <div className="font-medium text-gray-900">{product.name}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              product.inventoryCount === 0 
                                ? 'bg-red-100 text-red-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {product.inventoryCount}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {product.inventoryThreshold}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <Link 
                              href={`/admin/merchants/retail/${merchantId}/products/${product.id}/edit`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              Update Stock
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  {lowStockProducts.length > 5 && (
                    <div className="px-6 py-3 bg-gray-50 text-right">
                      <Link 
                        href={`/admin/merchants/retail/${merchantId}/inventory?filter=low-stock`}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        View all low stock items →
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'products' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Products</h2>
              <Link
                href={`/admin/merchants/retail/${merchantId}/products/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Add Product
              </Link>
            </div>
            
            {products && products.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        SKU
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map((product) => (
                      <tr key={product.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {product.images?.[0] && (
                              <img
                                src={product.images[0]}
                                alt={product.name}
                                className="h-10 w-10 rounded-md mr-3 object-cover"
                              />
                            )}
                            <div>
                              <div className="font-medium text-gray-900">{product.name}</div>
                              <div className="text-sm text-gray-500">{product.category}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {product.sku}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {product.salePrice ? (
                            <div>
                              <span className="text-red-600 font-medium">${product.salePrice.toFixed(2)}</span>
                              <span className="text-gray-500 line-through ml-2">${product.price.toFixed(2)}</span>
                            </div>
                          ) : (
                            <span className="text-gray-900">${product.price.toFixed(2)}</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            product.inventoryCount === 0 
                              ? 'bg-red-100 text-red-800' 
                              : product.inventoryCount <= product.inventoryThreshold
                              ? 'bg-yellow-100 text-yellow-800'
                              : 'bg-green-100 text-green-800'
                          }`}>
                            {product.inventoryCount}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            product.available 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.available ? 'Available' : 'Unavailable'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link 
                            href={`/admin/merchants/retail/${merchantId}/products/${product.id}`}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            View
                          </Link>
                          <Link 
                            href={`/admin/merchants/retail/${merchantId}/products/${product.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            Edit
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No products found. Add your first product to get started.</p>
              </div>
            )}
          </div>
        )}
        
        {activeTab === 'inventory' && (
          <div className="text-center py-8 text-gray-500">
            <p>Inventory management coming soon.</p>
          </div>
        )}
        
        {activeTab === 'orders' && (
          <div className="text-center py-8 text-gray-500">
            <p>Order management coming soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
