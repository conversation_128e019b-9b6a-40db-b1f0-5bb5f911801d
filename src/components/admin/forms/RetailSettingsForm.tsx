'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, Controller, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  retailSettingsSchema,
  RetailSettings
} from '@/lib/validations/merchantSchema';
import { useUpdateMerchantMutation } from '@/lib/redux/api/endpoints/merchantApi';

interface RetailSettingsFormProps {
  merchantId: string;
  initialSettings: RetailSettings;
}

export default function RetailSettingsForm({ 
  merchantId, 
  initialSettings 
}: RetailSettingsFormProps) {
  const [updateMerchant, { isLoading }] = useUpdateMerchantMutation();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    control,
    formState: { errors, isDirty }
  } = useForm<RetailSettings>({
    resolver: zodResolver(retailSettingsSchema),
    defaultValues: {
      ...initialSettings,
      shippingMethods: initialSettings.shippingMethods || []
    }
  });
  
  // Set up field array for shipping methods
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'shippingMethods',
  });
  
  // Form submission handler
  const onSubmit: SubmitHandler<RetailSettings> = async (data) => {
    try {
      await updateMerchant({
        id: merchantId,
        data: {
          settings: data
        },
      }).unwrap();
      
      setSuccess('Settings updated successfully');
      setError(null);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to update settings. Please try again.');
      setSuccess(null);
      console.error('Error updating settings:', err);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Retail Store Settings</h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="flex flex-col space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('inventoryManagement')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Inventory Management</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('allowBackorders')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Allow Backorders</span>
              </label>
            </div>
          </div>
          
          <div>
            <label htmlFor="returnPolicy" className="block text-sm font-medium text-gray-700 mb-1">
              Return Policy
            </label>
            <textarea
              id="returnPolicy"
              {...register('returnPolicy')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.returnPolicy ? 'border-red-500' : ''}`}
              placeholder="e.g., 30-day returns on unused items"
            />
            {errors.returnPolicy && (
              <p className="mt-1 text-sm text-red-600">{errors.returnPolicy.message}</p>
            )}
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Shipping Methods
          </label>
          
          <div className="space-y-2 mb-2">
            {fields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`shippingMethods.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Standard, Express, Next Day"
                />
                <button
                  type="button"
                  onClick={() => remove(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={() => append('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Shipping Method
          </button>
        </div>
        
        <div className="mb-6">
          <h3 className="text-md font-medium mb-2">Opening Hours</h3>
          <div className="space-y-3">
            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
              <div key={day} className="grid grid-cols-3 gap-4 items-center">
                <div className="text-sm font-medium">{day}</div>
                <div>
                  <label htmlFor={`${day}-open`} className="sr-only">Opening Time</label>
                  <Controller
                    name={`openingHours.${day}.open` as any}
                    control={control}
                    render={({ field }) => (
                      <input
                        id={`${day}-open`}
                        type="time"
                        {...field}
                        className="w-full p-2 border rounded-md"
                      />
                    )}
                  />
                </div>
                <div>
                  <label htmlFor={`${day}-close`} className="sr-only">Closing Time</label>
                  <Controller
                    name={`openingHours.${day}.close` as any}
                    control={control}
                    render={({ field }) => (
                      <input
                        id={`${day}-close`}
                        type="time"
                        {...field}
                        className="w-full p-2 border rounded-md"
                      />
                    )}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
