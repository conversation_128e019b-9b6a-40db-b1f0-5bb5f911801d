'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  restaurantSettingsSchema,
  RestaurantSettings
} from '@/lib/validations/merchantSchema';
import { useUpdateMerchantMutation } from '@/lib/redux/api/endpoints/merchantApi';

interface RestaurantSettingsFormProps {
  merchantId: string;
  initialSettings: RestaurantSettings;
}

export default function RestaurantSettingsForm({ 
  merchantId, 
  initialSettings 
}: RestaurantSettingsFormProps) {
  const [updateMerchant, { isLoading }] = useUpdateMerchantMutation();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    control,
    formState: { errors, isDirty }
  } = useForm<RestaurantSettings>({
    resolver: zodResolver(restaurantSettingsSchema),
    defaultValues: initialSettings
  });
  
  // Form submission handler
  const onSubmit: SubmitHandler<RestaurantSettings> = async (data) => {
    try {
      await updateMerchant({
        id: merchantId,
        data: {
          settings: data
        },
      }).unwrap();
      
      setSuccess('Settings updated successfully');
      setError(null);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to update settings. Please try again.');
      setSuccess(null);
      console.error('Error updating settings:', err);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Restaurant Settings</h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="cuisineType" className="block text-sm font-medium text-gray-700 mb-1">
              Cuisine Type
            </label>
            <input
              id="cuisineType"
              {...register('cuisineType')}
              className={`w-full p-2 border rounded-md ${errors.cuisineType ? 'border-red-500' : ''}`}
              placeholder="e.g., Italian, Japanese, Mexican"
            />
            {errors.cuisineType && (
              <p className="mt-1 text-sm text-red-600">{errors.cuisineType.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="priceRange" className="block text-sm font-medium text-gray-700 mb-1">
              Price Range
            </label>
            <select
              id="priceRange"
              {...register('priceRange')}
              className={`w-full p-2 border rounded-md ${errors.priceRange ? 'border-red-500' : ''}`}
            >
              <option value="">Select price range</option>
              <option value="$">$ (Inexpensive)</option>
              <option value="$$">$$ (Moderate)</option>
              <option value="$$$">$$$ (Expensive)</option>
              <option value="$$$$">$$$$ (Very Expensive)</option>
            </select>
            {errors.priceRange && (
              <p className="mt-1 text-sm text-red-600">{errors.priceRange.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="seatingCapacity" className="block text-sm font-medium text-gray-700 mb-1">
              Seating Capacity
            </label>
            <input
              id="seatingCapacity"
              type="number"
              {...register('seatingCapacity', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.seatingCapacity ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.seatingCapacity && (
              <p className="mt-1 text-sm text-red-600">{errors.seatingCapacity.message}</p>
            )}
          </div>
          
          <div className="flex flex-col space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('reservationEnabled')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Reservations</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('deliveryEnabled')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Delivery</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('takeoutEnabled')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Takeout</span>
              </label>
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <h3 className="text-md font-medium mb-2">Opening Hours</h3>
          <div className="space-y-3">
            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
              <div key={day} className="grid grid-cols-3 gap-4 items-center">
                <div className="text-sm font-medium">{day}</div>
                <div>
                  <label htmlFor={`${day}-open`} className="sr-only">Opening Time</label>
                  <Controller
                    name={`openingHours.${day}.open` as any}
                    control={control}
                    render={({ field }) => (
                      <input
                        id={`${day}-open`}
                        type="time"
                        {...field}
                        className="w-full p-2 border rounded-md"
                      />
                    )}
                  />
                </div>
                <div>
                  <label htmlFor={`${day}-close`} className="sr-only">Closing Time</label>
                  <Controller
                    name={`openingHours.${day}.close` as any}
                    control={control}
                    render={({ field }) => (
                      <input
                        id={`${day}-close`}
                        type="time"
                        {...field}
                        className="w-full p-2 border rounded-md"
                      />
                    )}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
