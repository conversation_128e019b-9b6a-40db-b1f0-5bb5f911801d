'use client';

import { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Reservation, reservationSchema } from '@/lib/validations/reservationSchema';
import { 
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useGetTablesQuery,
  useGetTableAvailabilityQuery
} from '@/lib/redux/api/endpoints/restaurantApi';
import { format } from 'date-fns';

interface ReservationFormProps {
  merchantId: string;
  initialData?: Reservation & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function ReservationForm({ 
  merchantId, 
  initialData, 
  onSuccess,
  mode
}: ReservationFormProps) {
  const [createReservation, { isLoading: isCreating }] = useCreateReservationMutation();
  const [updateReservation, { isLoading: isUpdating }] = useUpdateReservationMutation();
  
  const { data: tables } = useGetTablesQuery(merchantId);
  
  const [selectedDate, setSelectedDate] = useState<string>(
    initialData?.date || format(new Date(), 'yyyy-MM-dd')
  );
  const [partySize, setPartySize] = useState<number>(
    initialData?.partySize || 2
  );
  
  const { data: availableTimeSlots, isLoading: isLoadingTimeSlots } = useGetTableAvailabilityQuery(
    { merchantId, date: selectedDate, partySize },
    { skip: !selectedDate || !partySize }
  );
  
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  const isLoading = isCreating || isUpdating || isLoadingTimeSlots;
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    setValue,
    watch,
    formState: { errors, isDirty }
  } = useForm<Reservation>({
    resolver: zodResolver(reservationSchema),
    defaultValues: initialData || {
      tableId: '',
      userId: '',
      date: format(new Date(), 'yyyy-MM-dd'),
      time: '',
      duration: 60,
      partySize: 2,
      status: 'pending',
      specialRequests: '',
      customerName: '',
      customerEmail: '',
      customerPhone: '',
    }
  });
  
  // Watch for changes to date and party size
  const watchedDate = watch('date');
  const watchedPartySize = watch('partySize');
  
  // Update selected date and party size when form values change
  useEffect(() => {
    if (watchedDate !== selectedDate) {
      setSelectedDate(watchedDate);
    }
    
    if (watchedPartySize !== partySize) {
      setPartySize(watchedPartySize);
    }
  }, [watchedDate, watchedPartySize, selectedDate, partySize]);
  
  // Handle form submission
  const onSubmit: SubmitHandler<Reservation> = async (data) => {
    try {
      setError(null);
      
      if (mode === 'create') {
        const result = await createReservation({
          merchantId,
          ...data,
        }).unwrap();
        
        setSuccess('Reservation created successfully');
        if (onSuccess) onSuccess(result);
      } else if (mode === 'edit' && initialData?.id) {
        const result = await updateReservation({
          merchantId,
          reservationId: initialData.id,
          data,
        }).unwrap();
        
        setSuccess('Reservation updated successfully');
        if (onSuccess) onSuccess(result);
      }
    } catch (err) {
      console.error('Error submitting reservation:', err);
      setError('Failed to save reservation. Please try again.');
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-semibold mb-4">
        {mode === 'create' ? 'Create Reservation' : 'Edit Reservation'}
      </h2>
      
      {error && (
        <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-100 text-green-700 rounded-md">
          {success}
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Date *
            </label>
            <input
              id="date"
              type="date"
              {...register('date')}
              className={`w-full p-2 border rounded-md ${errors.date ? 'border-red-500' : ''}`}
              min={format(new Date(), 'yyyy-MM-dd')}
            />
            {errors.date && (
              <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="partySize" className="block text-sm font-medium text-gray-700 mb-1">
              Party Size *
            </label>
            <input
              id="partySize"
              type="number"
              min="1"
              {...register('partySize', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.partySize ? 'border-red-500' : ''}`}
            />
            {errors.partySize && (
              <p className="mt-1 text-sm text-red-600">{errors.partySize.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-1">
              Time *
            </label>
            <select
              id="time"
              {...register('time')}
              className={`w-full p-2 border rounded-md ${errors.time ? 'border-red-500' : ''}`}
              disabled={isLoadingTimeSlots || !availableTimeSlots || availableTimeSlots.length === 0}
            >
              <option value="">Select a time</option>
              {availableTimeSlots?.map((slot, index) => (
                <option key={index} value={slot.time}>
                  {slot.time}
                </option>
              ))}
            </select>
            {errors.time && (
              <p className="mt-1 text-sm text-red-600">{errors.time.message}</p>
            )}
            {!isLoadingTimeSlots && availableTimeSlots?.length === 0 && (
              <p className="mt-1 text-sm text-amber-600">No available times for this date and party size</p>
            )}
          </div>
          
          <div>
            <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
              Duration (minutes) *
            </label>
            <input
              id="duration"
              type="number"
              min="30"
              step="30"
              {...register('duration', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.duration ? 'border-red-500' : ''}`}
            />
            {errors.duration && (
              <p className="mt-1 text-sm text-red-600">{errors.duration.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="tableId" className="block text-sm font-medium text-gray-700 mb-1">
              Table *
            </label>
            <select
              id="tableId"
              {...register('tableId')}
              className={`w-full p-2 border rounded-md ${errors.tableId ? 'border-red-500' : ''}`}
            >
              <option value="">Select a table</option>
              {tables?.filter(table => table.capacity >= partySize).map((table) => (
                <option key={table.id} value={table.id}>
                  Table {table.number} (Capacity: {table.capacity})
                </option>
              ))}
            </select>
            {errors.tableId && (
              <p className="mt-1 text-sm text-red-600">{errors.tableId.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status *
            </label>
            <select
              id="status"
              {...register('status')}
              className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : ''}`}
            >
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="cancelled">Cancelled</option>
              <option value="completed">Completed</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Name *
            </label>
            <input
              id="customerName"
              type="text"
              {...register('customerName')}
              className={`w-full p-2 border rounded-md ${errors.customerName ? 'border-red-500' : ''}`}
            />
            {errors.customerName && (
              <p className="mt-1 text-sm text-red-600">{errors.customerName.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Email *
            </label>
            <input
              id="customerEmail"
              type="email"
              {...register('customerEmail')}
              className={`w-full p-2 border rounded-md ${errors.customerEmail ? 'border-red-500' : ''}`}
            />
            {errors.customerEmail && (
              <p className="mt-1 text-sm text-red-600">{errors.customerEmail.message}</p>
            )}
          </div>
          
          <div>
            <label htmlFor="customerPhone" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Phone
            </label>
            <input
              id="customerPhone"
              type="tel"
              {...register('customerPhone')}
              className={`w-full p-2 border rounded-md ${errors.customerPhone ? 'border-red-500' : ''}`}
            />
            {errors.customerPhone && (
              <p className="mt-1 text-sm text-red-600">{errors.customerPhone.message}</p>
            )}
          </div>
          
          <div className="md:col-span-2">
            <label htmlFor="specialRequests" className="block text-sm font-medium text-gray-700 mb-1">
              Special Requests
            </label>
            <textarea
              id="specialRequests"
              {...register('specialRequests')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.specialRequests ? 'border-red-500' : ''}`}
            />
            {errors.specialRequests && (
              <p className="mt-1 text-sm text-red-600">{errors.specialRequests.message}</p>
            )}
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Reservation' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
