'use client';

import { useState } from 'react';
import { useForm, SubmitHandler, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  digitalSettingsSchema,
  DigitalSettings
} from '@/lib/validations/merchantSchema';
import { useUpdateMerchantMutation } from '@/lib/redux/api/endpoints/merchantApi';

interface DigitalSettingsFormProps {
  merchantId: string;
  initialSettings: DigitalSettings;
}

export default function DigitalSettingsForm({ 
  merchantId, 
  initialSettings 
}: DigitalSettingsFormProps) {
  const [updateMerchant, { isLoading }] = useUpdateMerchantMutation();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Set up react-hook-form with zod validation
  const { 
    register, 
    handleSubmit, 
    control,
    formState: { errors, isDirty }
  } = useForm<DigitalSettings>({
    resolver: zodResolver(digitalSettingsSchema),
    defaultValues: {
      ...initialSettings,
      licenseTypes: initialSettings.licenseTypes || []
    }
  });
  
  // Set up field array for license types
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'licenseTypes',
  });
  
  // Form submission handler
  const onSubmit: SubmitHandler<DigitalSettings> = async (data) => {
    try {
      await updateMerchant({
        id: merchantId,
        data: {
          settings: data
        },
      }).unwrap();
      
      setSuccess('Settings updated successfully');
      setError(null);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to update settings. Please try again.');
      setSuccess(null);
      console.error('Error updating settings:', err);
    }
  };
  
  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Digital Store Settings</h2>
      
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div className="flex flex-col space-y-4">
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('automaticDelivery')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Automatic Delivery</span>
              </label>
            </div>
            
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  {...register('watermarkEnabled')}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Enable Watermarking</span>
              </label>
            </div>
          </div>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="downloadLimitPerPurchase" className="block text-sm font-medium text-gray-700 mb-1">
                Download Limit Per Purchase
              </label>
              <input
                id="downloadLimitPerPurchase"
                type="number"
                {...register('downloadLimitPerPurchase', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.downloadLimitPerPurchase ? 'border-red-500' : ''}`}
                min="0"
                placeholder="0 for unlimited"
              />
              {errors.downloadLimitPerPurchase && (
                <p className="mt-1 text-sm text-red-600">{errors.downloadLimitPerPurchase.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="downloadExpiryDays" className="block text-sm font-medium text-gray-700 mb-1">
                Download Expiry (Days)
              </label>
              <input
                id="downloadExpiryDays"
                type="number"
                {...register('downloadExpiryDays', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.downloadExpiryDays ? 'border-red-500' : ''}`}
                min="0"
                placeholder="0 for never"
              />
              {errors.downloadExpiryDays && (
                <p className="mt-1 text-sm text-red-600">{errors.downloadExpiryDays.message}</p>
              )}
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            License Types
          </label>
          
          <div className="space-y-2 mb-2">
            {fields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`licenseTypes.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Standard, Extended, Commercial"
                />
                <button
                  type="button"
                  onClick={() => remove(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={() => append('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add License Type
          </button>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || !isDirty}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : 'Save Settings'}
          </button>
        </div>
      </form>
    </div>
  );
}
