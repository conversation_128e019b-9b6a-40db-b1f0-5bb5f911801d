'use client';

import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { supabase } from '@/lib/supabase/client';
import { setUser, setLoading, setError } from '@/lib/redux/slices/authSlice';

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch();

  useEffect(() => {
    // Check for active session on mount
    const checkSession = async () => {
      try {
        dispatch(setLoading(true));
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          throw error;
        }
        
        if (session) {
          dispatch(setUser(session.user));
        } else {
          dispatch(setUser(null));
        }
      } catch (error) {
        console.error('Auth error:', error);
        dispatch(setError(error instanceof Error ? error.message : 'Authentication error'));
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session) {
          dispatch(setUser(session.user));
        } else {
          dispatch(setUser(null));
        }
      }
    );

    // Cleanup subscription
    return () => {
      subscription.unsubscribe();
    };
  }, [dispatch]);

  return <>{children}</>;
}
