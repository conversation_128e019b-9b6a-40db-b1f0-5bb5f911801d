'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useCreateMerchantMutation } from '@/lib/redux/api/endpoints/merchantApi';
import MerchantTypeSelector from '@/components/admin/dashboard/MerchantTypeSelector';
import {
  merchantSchema,
  restaurantSettingsSchema,
  retailSettingsSchema,
  serviceSettingsSchema,
  digitalSettingsSchema,
  convenienceSettingsSchema
} from '@/lib/validations/merchantSchema';

type MerchantType = 'restaurant' | 'retail' | 'service' | 'digital' | 'custom' | 'convenience';

// Create a schema for the form's first step (without type-specific settings)
const createMerchantSchema = merchantSchema.omit({ settings: true });
type CreateMerchantInput = z.infer<typeof createMerchantSchema>;

export default function CreateMerchantPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const initialType = searchParams.get('type') as MerchantType | null;

  const [createMerchant, { isLoading }] = useCreateMerchantMutation();

  const [selectedType, setSelectedType] = useState<MerchantType | null>(initialType);
  const [step, setStep] = useState<'type' | 'details'>(initialType ? 'details' : 'type');
  const [error, setError] = useState<string | null>(null);

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue
  } = useForm<CreateMerchantInput>({
    resolver: zodResolver(createMerchantSchema),
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      address: '',
      ownerId: '',
      status: 'pending',
      logo: '',
      type: selectedType || undefined,
    }
  });

  // Update form when type changes
  if (selectedType && !initialType) {
    setValue('type', selectedType);
  }

  const handleTypeSelect = (type: MerchantType) => {
    setSelectedType(type);
    setValue('type', type);
  };

  const handleContinue = () => {
    if (selectedType) {
      setStep('details');
    }
  };

  // Form submission handler
  const onSubmit: SubmitHandler<CreateMerchantInput> = async (data) => {
    if (!selectedType) {
      setError('Please select a merchant type');
      return;
    }

    try {
      const result = await createMerchant({
        ...data,
        type: selectedType,
        settings: getDefaultSettings(selectedType),
      }).unwrap();

      // Redirect to the new merchant page
      router.push(`/admin/merchants/${selectedType}/${result.id}`);
    } catch (err) {
      setError('Failed to create merchant. Please try again.');
      console.error('Error creating merchant:', err);
    }
  };

  // Get default settings based on merchant type
  const getDefaultSettings = (type: MerchantType) => {
    switch (type) {
      case 'restaurant':
        return {
          cuisineType: '',
          priceRange: '',
          seatingCapacity: 0,
          reservationEnabled: true,
          deliveryEnabled: false,
          takeoutEnabled: true,
          openingHours: {
            Monday: { open: '09:00', close: '22:00' },
            Tuesday: { open: '09:00', close: '22:00' },
            Wednesday: { open: '09:00', close: '22:00' },
            Thursday: { open: '09:00', close: '22:00' },
            Friday: { open: '09:00', close: '23:00' },
            Saturday: { open: '09:00', close: '23:00' },
            Sunday: { open: '09:00', close: '22:00' },
          },
        };
      case 'retail':
        return {
          inventoryManagement: true,
          allowBackorders: false,
          shippingMethods: ['Standard', 'Express'],
          returnPolicy: '30-day returns on unused items',
          openingHours: {
            Monday: { open: '09:00', close: '20:00' },
            Tuesday: { open: '09:00', close: '20:00' },
            Wednesday: { open: '09:00', close: '20:00' },
            Thursday: { open: '09:00', close: '20:00' },
            Friday: { open: '09:00', close: '21:00' },
            Saturday: { open: '09:00', close: '21:00' },
            Sunday: { open: '10:00', close: '18:00' },
          },
        };
      case 'service':
        return {
          appointmentEnabled: true,
          serviceLocations: ['In-store'],
          cancellationPolicy: '24-hour cancellation policy',
          openingHours: {
            Monday: { open: '09:00', close: '18:00' },
            Tuesday: { open: '09:00', close: '18:00' },
            Wednesday: { open: '09:00', close: '18:00' },
            Thursday: { open: '09:00', close: '18:00' },
            Friday: { open: '09:00', close: '18:00' },
            Saturday: { open: '10:00', close: '16:00' },
            Sunday: { open: '00:00', close: '00:00' },
          },
        };
      case 'digital':
        return {
          automaticDelivery: true,
          downloadLimitPerPurchase: 5,
          downloadExpiryDays: 30,
          watermarkEnabled: false,
          licenseTypes: ['Standard', 'Extended'],
        };
      case 'convenience':
        return {
          inventoryManagement: true,
          deliveryEnabled: true,
          deliveryRadius: 5,
          minimumOrderAmount: 10,
          openingHours: {
            Monday: { open: '07:00', close: '23:00' },
            Tuesday: { open: '07:00', close: '23:00' },
            Wednesday: { open: '07:00', close: '23:00' },
            Thursday: { open: '07:00', close: '23:00' },
            Friday: { open: '07:00', close: '23:00' },
            Saturday: { open: '07:00', close: '23:00' },
            Sunday: { open: '07:00', close: '23:00' },
          },
        };
      case 'custom':
      default:
        return {};
    }
  };

  // Get type display name
  const getTypeDisplayName = () => {
    if (!selectedType) return '';

    switch (selectedType) {
      case 'restaurant':
        return 'Restaurant';
      case 'retail':
        return 'Retail Store';
      case 'service':
        return 'Service Provider';
      case 'digital':
        return 'Digital Store';
      case 'convenience':
        return 'Convenience Store';
      case 'custom':
        return 'Custom Merchant';
      default:
        return 'Merchant';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Create New Merchant</h1>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {step === 'type' ? (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-4">Select Merchant Type</h2>
          <MerchantTypeSelector
            selectedType={selectedType}
            onSelect={handleTypeSelect}
            onContinue={handleContinue}
          />
          <div className="flex justify-end mt-6">
            <button
              onClick={handleContinue}
              disabled={!selectedType}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Continue
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-4">
            {getTypeDisplayName()} Details
          </h2>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Merchant Name *
                </label>
                <input
                  id="name"
                  {...register('name')}
                  className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  id="email"
                  type="email"
                  {...register('email')}
                  className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : ''}`}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number *
                </label>
                <input
                  id="phoneNumber"
                  {...register('phoneNumber')}
                  className={`w-full p-2 border rounded-md ${errors.phoneNumber ? 'border-red-500' : ''}`}
                />
                {errors.phoneNumber && (
                  <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="ownerId" className="block text-sm font-medium text-gray-700 mb-1">
                  Owner ID *
                </label>
                <input
                  id="ownerId"
                  {...register('ownerId')}
                  className={`w-full p-2 border rounded-md ${errors.ownerId ? 'border-red-500' : ''}`}
                />
                {errors.ownerId && (
                  <p className="mt-1 text-sm text-red-600">{errors.ownerId.message}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Address *
                </label>
                <textarea
                  id="address"
                  {...register('address')}
                  rows={3}
                  className={`w-full p-2 border rounded-md ${errors.address ? 'border-red-500' : ''}`}
                />
                {errors.address && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="logo" className="block text-sm font-medium text-gray-700 mb-1">
                  Logo URL
                </label>
                <input
                  id="logo"
                  type="url"
                  {...register('logo')}
                  className={`w-full p-2 border rounded-md ${errors.logo ? 'border-red-500' : ''}`}
                />
                {errors.logo && (
                  <p className="mt-1 text-sm text-red-600">{errors.logo.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  id="status"
                  {...register('status')}
                  className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : ''}`}
                >
                  <option value="pending">Pending</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
                {errors.status && (
                  <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setStep('type')}
                className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creating...' : 'Create Merchant'}
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
