'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/merchantApi';

interface MerchantTypePageProps {
  params: {
    type: string;
  };
}

export default function MerchantTypePage({ params }: MerchantTypePageProps) {
  const { type } = params;
  const [searchTerm, setSearchTerm] = useState('');
  
  const { data: merchants, isLoading, error } = useGetMerchantsQuery();
  
  // Filter merchants by type and search term
  const filteredMerchants = merchants?.filter(merchant => 
    merchant.type === type && 
    (searchTerm === '' || 
      merchant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      merchant.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Get type display name
  const getTypeDisplayName = () => {
    switch (type) {
      case 'restaurant':
        return 'Restaurants';
      case 'retail':
        return 'Retail Stores';
      case 'service':
        return 'Service Providers';
      case 'digital':
        return 'Digital Stores';
      case 'convenience':
        return 'Convenience Stores';
      case 'custom':
        return 'Custom Merchants';
      default:
        return 'Merchants';
    }
  };
  
  // Get type icon
  const getTypeIcon = () => {
    switch (type) {
      case 'restaurant':
        return '🍽️';
      case 'retail':
        return '🛍️';
      case 'service':
        return '🔧';
      case 'digital':
        return '💻';
      case 'convenience':
        return '🏪';
      case 'custom':
        return '🔄';
      default:
        return '🏢';
    }
  };
  
  if (isLoading) {
    return <div className="p-6">Loading merchants...</div>;
  }
  
  if (error) {
    return <div className="p-6 text-red-500">Error loading merchants: {JSON.stringify(error)}</div>;
  }
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <span className="text-3xl mr-3">{getTypeIcon()}</span>
          <h1 className="text-2xl font-bold">{getTypeDisplayName()}</h1>
        </div>
        <Link 
          href={`/admin/merchants/create?type=${type}`} 
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Add {type.charAt(0).toUpperCase() + type.slice(1)}
        </Link>
      </div>
      
      <div className="mb-6">
        <div className="relative">
          <input
            type="text"
            placeholder={`Search ${getTypeDisplayName().toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <span className="absolute left-3 top-2.5">🔍</span>
        </div>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Owner
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredMerchants && filteredMerchants.length > 0 ? (
                filteredMerchants.map((merchant) => (
                  <tr key={merchant.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {merchant.logo && (
                          <img
                            src={merchant.logo}
                            alt={merchant.name}
                            className="h-10 w-10 rounded-full mr-3 object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium text-gray-900">{merchant.name}</div>
                          <div className="text-sm text-gray-500">{merchant.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {merchant.address}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        merchant.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : merchant.status === 'pending' 
                          ? 'bg-yellow-100 text-yellow-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {merchant.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {merchant.ownerId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link 
                        href={`/admin/merchants/${type}/${merchant.id}`}
                        className="text-blue-600 hover:text-blue-900 mr-4"
                      >
                        View
                      </Link>
                      <Link 
                        href={`/admin/merchants/${type}/${merchant.id}/edit`}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        Edit
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    No {getTypeDisplayName().toLowerCase()} found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
