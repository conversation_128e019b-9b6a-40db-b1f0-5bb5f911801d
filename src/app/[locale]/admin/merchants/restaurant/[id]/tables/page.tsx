'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/merchantApi';
import TableManagement from '@/components/admin/restaurant/TableManagement';
import { Button } from '@/components/ui/button';

interface TablesPageProps {
  params: {
    id: string;
  };
}

export default function TablesPage({ params }: TablesPageProps) {
  const { id } = params;
  const router = useRouter();
  
  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  
  // Get the merchant
  const merchant = merchants?.find(m => m.id === id);
  
  if (isLoadingMerchants) {
    return <div className="p-4">Loading merchant data...</div>;
  }
  
  if (!merchant) {
    return <div className="p-4">Merchant not found.</div>;
  }
  
  if (merchant.type !== 'restaurant') {
    return <div className="p-4">This feature is only available for restaurant merchants.</div>;
  }
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{merchant.name} - Tables</h1>
          <p className="text-gray-500">Manage restaurant tables</p>
        </div>
        <Link href={`/admin/merchants/restaurant/${id}`}>
          <Button variant="outline">Back to Dashboard</Button>
        </Link>
      </div>
      
      <TableManagement merchantId={id} />
    </div>
  );
}
