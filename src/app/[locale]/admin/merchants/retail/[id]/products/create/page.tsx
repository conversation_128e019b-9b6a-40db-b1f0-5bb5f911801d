'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ProductForm from '@/components/admin/forms/ProductForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/merchantApi';

interface CreateProductPageProps {
  params: {
    id: string;
  };
}

export default function CreateProductPage({ params }: CreateProductPageProps) {
  const { id } = params;
  const router = useRouter();
  const { data: merchants, isLoading } = useGetMerchantsQuery();
  
  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'retail');
  
  const handleSuccess = () => {
    // Redirect to the products list
    router.push(`/admin/merchants/retail/${id}`);
  };
  
  if (isLoading) {
    return <div className="p-6">Loading merchant data...</div>;
  }
  
  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Retail store not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }
  
  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/retail/${id}`} className="text-blue-600 hover:underline">
          ← Back to retail store
        </Link>
      </div>
      
      <h1 className="text-2xl font-bold mb-6">Add Product to {merchant.name}</h1>
      
      <ProductForm 
        merchantId={id} 
        mode="create" 
        onSuccess={handleSuccess} 
      />
    </div>
  );
}
