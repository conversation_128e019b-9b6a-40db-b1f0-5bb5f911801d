import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { locales } from '@/i18n/config';
import { getMessages } from 'next-intl/server';
import ClientLayout from './ClientLayout';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

export async function generateMetadata({ params }: { params: { locale: string } }) {
  return {
    title: 'ADC Shop Merchants',
    description: 'ADC Shop Merchants Portal',
  };
}

type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

export default async function RootLayout({ children, params }: Props) {
  // Use a safer approach to get the locale
  const localeParam = params?.locale || 'en';

  try {
    // Get messages for the locale
    const messages = await getMessages({ locale: localeParam });
    const fontClasses = `${geistSans.variable} ${geistMono.variable} antialiased`;

    // Use the client component to handle the rendering
    return (
      <ClientLayout
        locale={localeParam}
        messages={messages}
        fontClasses={fontClasses}
      >
        {children}
      </ClientLayout>
    );
  } catch (error) {
    console.error('Error loading messages:', error);

    // Fallback to a basic layout if messages can't be loaded
    return (
      <html lang={localeParam}>
        <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
          <div className="p-4">
            <h1 className="text-xl font-bold">Error loading translations</h1>
            <p>Please try refreshing the page</p>
          </div>
        </body>
      </html>
    );
  }
}
