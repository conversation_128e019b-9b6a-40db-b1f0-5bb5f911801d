'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Layout, 
  Bell, 
  User, 
  CreditCard, 
  Store, 
  Mail, 
  MessageSquare, 
  Shield, 
  HelpCircle,
  ChevronRight
} from 'lucide-react';

export default function SettingsPage() {
  const settingsCategories = [
    {
      title: 'Appearance',
      description: 'Customize the look and feel of your dashboard',
      icon: <Layout className="h-5 w-5" />,
      items: [
        {
          name: 'Navigation',
          description: 'Change navigation type and display options',
          href: '/app/restaurant/settings/navigation',
        },
        {
          name: 'Theme',
          description: 'Customize colors, fonts, and visual elements',
          href: '/app/restaurant/settings/theme',
        },
      ],
    },
    {
      title: 'Account',
      description: 'Manage your account settings and preferences',
      icon: <User className="h-5 w-5" />,
      items: [
        {
          name: 'Profile',
          description: 'Update your personal information',
          href: '/app/restaurant/settings/profile',
        },
        {
          name: 'Password',
          description: 'Change your password and security settings',
          href: '/app/restaurant/settings/password',
        },
      ],
    },
    {
      title: 'Notifications',
      description: 'Configure how you receive notifications',
      icon: <Bell className="h-5 w-5" />,
      items: [
        {
          name: 'Email Notifications',
          description: 'Manage email notification preferences',
          href: '/app/restaurant/settings/notifications/email',
        },
        {
          name: 'Push Notifications',
          description: 'Configure in-app and mobile notifications',
          href: '/app/restaurant/settings/notifications/push',
        },
      ],
    },
    {
      title: 'Billing',
      description: 'Manage your subscription and payment methods',
      icon: <CreditCard className="h-5 w-5" />,
      items: [
        {
          name: 'Subscription',
          description: 'View and change your subscription plan',
          href: '/app/restaurant/settings/billing/subscription',
        },
        {
          name: 'Payment Methods',
          description: 'Add or update your payment information',
          href: '/app/restaurant/settings/billing/payment',
        },
      ],
    },
  ];

  return (
    <div className="p-6 font-be-vietnam">
      <div className="mb-6">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Settings</h1>
        <p className="text-[#8a745c] text-sm font-normal leading-normal">Manage your account and application preferences</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {settingsCategories.map((category) => (
          <Card key={category.title} className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-[#8a745c]">{category.icon}</span>
                <CardTitle className="text-[#181510]">{category.title}</CardTitle>
              </div>
              <CardDescription className="text-[#8a745c]">{category.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {category.items.map((item) => (
                  <li key={item.name}>
                    <Link 
                      href={item.href}
                      className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                    >
                      <div>
                        <div className="font-medium text-[#181510]">{item.name}</div>
                        <div className="text-sm text-[#8a745c]">{item.description}</div>
                      </div>
                      <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                    </Link>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Store className="h-5 w-5 text-[#8a745c]" />
              <CardTitle className="text-[#181510]">Restaurant Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/app/restaurant/settings/restaurant/details"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">Restaurant Details</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
              <li>
                <Link 
                  href="/app/restaurant/settings/restaurant/hours"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">Business Hours</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-[#8a745c]" />
              <CardTitle className="text-[#181510]">Communications</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/app/restaurant/settings/communications/email"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">Email Templates</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
              <li>
                <Link 
                  href="/app/restaurant/settings/communications/sms"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">SMS Settings</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-[#8a745c]" />
              <CardTitle className="text-[#181510]">Support</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              <li>
                <Link 
                  href="/app/restaurant/settings/support/help"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">Help Center</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
              <li>
                <Link 
                  href="/app/restaurant/settings/support/contact"
                  className="flex items-center justify-between p-3 rounded-md hover:bg-[#f1edea] transition-colors"
                >
                  <div className="font-medium text-[#181510]">Contact Support</div>
                  <ChevronRight className="h-4 w-4 text-[#8a745c]" />
                </Link>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
