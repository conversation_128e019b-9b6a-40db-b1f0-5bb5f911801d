'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { format, parseISO, isToday, isFuture, isPast, addDays } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import ReservationForm from '@/components/restaurant/ReservationForm';
import { toast } from 'sonner';

// Mock data for tables
const mockTables = [
  { id: 'table1', number: 1, capacity: 2, status: 'available', location: 'dining' },
  { id: 'table2', number: 2, capacity: 4, status: 'available', location: 'dining' },
  { id: 'table3', number: 3, capacity: 6, status: 'available', location: 'dining' },
  { id: 'table4', number: 4, capacity: 2, status: 'available', location: 'outdoor' },
  { id: 'table5', number: 5, capacity: 4, status: 'available', location: 'outdoor' },
];

// Mock data for reservations
const mockReservations = [
  {
    id: 'res1',
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '18:00',
    partySize: 2,
    tableId: 'table1',
    status: 'confirmed',
    specialRequests: 'No nuts please',
  },
  {
    id: 'res2',
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '19:30',
    partySize: 4,
    tableId: 'table2',
    status: 'pending',
    specialRequests: '',
  },
  {
    id: 'res3',
    customerName: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-16',
    time: '20:00',
    partySize: 6,
    tableId: 'table3',
    status: 'confirmed',
    specialRequests: 'Birthday celebration',
  },
  {
    id: 'res4',
    customerName: 'Alice Brown',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '18:30',
    partySize: 2,
    tableId: 'table4',
    status: 'confirmed',
    specialRequests: '',
  },
  {
    id: 'res5',
    customerName: 'Charlie Wilson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
    time: '19:00',
    partySize: 4,
    tableId: 'table5',
    status: 'pending',
    specialRequests: 'Outdoor seating preferred',
  },
];

export default function ReservationsPage() {
  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isAddReservationOpen, setIsAddReservationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('upcoming');

  // Use mock data for now, but in a real app we would use the API
  // const { data: reservations, isLoading } = useGetReservationsQuery(merchantId);
  const [reservations, setReservations] = useState(mockReservations);
  const [tables, setTables] = useState(mockTables);

  // Filter reservations
  const filteredReservations = reservations.filter(reservation => {
    // Filter by search term (customer name or email)
    const matchesSearch = searchTerm === '' ||
      reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customerEmail.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by status
    const matchesStatus = statusFilter === null || reservation.status === statusFilter;

    // Filter by date
    const matchesDate = !selectedDate || reservation.date === format(selectedDate, 'yyyy-MM-dd');

    // Filter by tab (upcoming or past)
    const isUpcoming = isFuture(parseISO(reservation.date)) ||
      (isToday(parseISO(reservation.date)) && reservation.time >= format(new Date(), 'HH:mm'));
    const matchesTab = (activeTab === 'upcoming' && isUpcoming) || (activeTab === 'past' && !isUpcoming);

    return matchesSearch && matchesStatus && matchesDate && matchesTab;
  });

  // Handle reservation creation success
  const handleCreateSuccess = () => {
    setIsAddReservationOpen(false);
    toast.success('Reservation created successfully');
    // In a real app, we would refetch the reservations
  };

  // Handle reservation cancellation
  const handleCancelReservation = (id: string) => {
    // In a real app, we would call the API
    setReservations(reservations.map(res =>
      res.id === id ? { ...res, status: 'cancelled' } : res
    ));
    toast.success('Reservation cancelled');
  };

  // Get reservation status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Confirmed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#181510]">Reservations</h1>
          <p className="text-[#8a745c]">Manage your restaurant reservations</p>
        </div>
        <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
          <DialogTrigger asChild>
            <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              New Reservation
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Create New Reservation</DialogTitle>
            </DialogHeader>
            <ReservationForm
              tables={tables.map(table => ({ id: table.id, number: table.number }))}
              onSuccess={handleCreateSuccess}
              onCancel={() => setIsAddReservationOpen(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-[#f1edea] p-4 rounded-lg mb-6">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
              <Input
                placeholder="Search by name or email"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-xs bg-[#fbfaf9] border-[#e2dcd4]"
              />
              <Select value={statusFilter || 'all'} onValueChange={(value) => setStatusFilter(value === 'all' ? null : value)}>
                <SelectTrigger className="w-[180px] bg-[#fbfaf9] border-[#e2dcd4]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Tabs defaultValue="upcoming" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4 bg-[#e2dcd4]">
                <TabsTrigger value="upcoming" className="data-[state=active]:bg-[#fbfaf9]">Upcoming</TabsTrigger>
                <TabsTrigger value="past" className="data-[state=active]:bg-[#fbfaf9]">Past</TabsTrigger>
              </TabsList>

              <TabsContent value="upcoming" className="mt-0">
                <div className="bg-[#fbfaf9] rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-[#e2dcd4] text-[#181510]">
                      <tr>
                        <th className="px-4 py-2 text-left">Customer</th>
                        <th className="px-4 py-2 text-left">Date & Time</th>
                        <th className="px-4 py-2 text-left">Party</th>
                        <th className="px-4 py-2 text-left">Status</th>
                        <th className="px-4 py-2 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredReservations.length > 0 ? (
                        filteredReservations.map((reservation) => (
                          <tr key={reservation.id} className="border-b border-[#e2dcd4]">
                            <td className="px-4 py-3">
                              <div className="font-medium">{reservation.customerName}</div>
                              <div className="text-sm text-[#8a745c]">{reservation.customerEmail}</div>
                            </td>
                            <td className="px-4 py-3">
                              <div>{format(parseISO(reservation.date), 'MMM d, yyyy')}</div>
                              <div className="text-sm text-[#8a745c]">{reservation.time}</div>
                            </td>
                            <td className="px-4 py-3">{reservation.partySize} people</td>
                            <td className="px-4 py-3">{getStatusBadge(reservation.status)}</td>
                            <td className="px-4 py-3">
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-xs h-8 px-2 border-[#e2dcd4]"
                                >
                                  Edit
                                </Button>
                                {reservation.status !== 'cancelled' && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-xs h-8 px-2 border-[#e2dcd4] hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                                    onClick={() => handleCancelReservation(reservation.id)}
                                  >
                                    Cancel
                                  </Button>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="px-4 py-3 text-center text-[#8a745c]">
                            No reservations found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </TabsContent>

              <TabsContent value="past" className="mt-0">
                <div className="bg-[#fbfaf9] rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-[#e2dcd4] text-[#181510]">
                      <tr>
                        <th className="px-4 py-2 text-left">Customer</th>
                        <th className="px-4 py-2 text-left">Date & Time</th>
                        <th className="px-4 py-2 text-left">Party</th>
                        <th className="px-4 py-2 text-left">Status</th>
                        <th className="px-4 py-2 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredReservations.length > 0 ? (
                        filteredReservations.map((reservation) => (
                          <tr key={reservation.id} className="border-b border-[#e2dcd4]">
                            <td className="px-4 py-3">
                              <div className="font-medium">{reservation.customerName}</div>
                              <div className="text-sm text-[#8a745c]">{reservation.customerEmail}</div>
                            </td>
                            <td className="px-4 py-3">
                              <div>{format(parseISO(reservation.date), 'MMM d, yyyy')}</div>
                              <div className="text-sm text-[#8a745c]">{reservation.time}</div>
                            </td>
                            <td className="px-4 py-3">{reservation.partySize} people</td>
                            <td className="px-4 py-3">{getStatusBadge(reservation.status)}</td>
                            <td className="px-4 py-3">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-8 px-2 border-[#e2dcd4]"
                              >
                                View
                              </Button>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={5} className="px-4 py-3 text-center text-[#8a745c]">
                            No past reservations found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        <div className="md:col-span-1">
          <div className="bg-[#f1edea] p-4 rounded-lg">
            <h2 className="text-lg font-medium mb-4 text-[#181510]">Reservation Calendar</h2>
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              className="bg-[#fbfaf9] rounded-md p-3"
            />
            <div className="mt-4">
              <h3 className="text-md font-medium mb-2 text-[#181510]">
                {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
              </h3>
              <div className="space-y-2">
                {filteredReservations.length > 0 ? (
                  filteredReservations.map((reservation) => (
                    <div key={reservation.id} className="bg-[#fbfaf9] p-3 rounded-md">
                      <div className="flex justify-between">
                        <div className="font-medium">{reservation.time}</div>
                        {getStatusBadge(reservation.status)}
                      </div>
                      <div className="text-sm text-[#8a745c]">{reservation.customerName}</div>
                      <div className="text-sm">{reservation.partySize} people</div>
                    </div>
                  ))
                ) : (
                  <div className="text-[#8a745c] text-center py-2">
                    No reservations for this date
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
