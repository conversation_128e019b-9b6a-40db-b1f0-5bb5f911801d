'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

// Mock staff data
const initialStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Chef',
    status: 'active',
    schedule: 'Mon-Fr<PERSON>, 9am-5pm',
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Server',
    status: 'active',
    schedule: '<PERSON><PERSON><PERSON><PERSON><PERSON>, 6pm-11pm',
  },
  {
    id: '3',
    name: '<PERSON>',
    role: '<PERSON><PERSON>',
    status: 'active',
    schedule: 'Wed-Sun, 7pm-2am',
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'Hostess',
    status: 'inactive',
    schedule: 'Mon-Fri, 5pm-10pm',
  },
  {
    id: '5',
    name: '<PERSON>',
    role: 'Manager',
    status: 'active',
    schedule: 'Mon-Sun, 10am-6pm',
  },
];

export default function StaffPage() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const t = useTranslations('restaurant');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [staffMembers, setStaffMembers] = useState(initialStaffData);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('staff-list');

  // Filter staff based on search term
  const filteredStaff = staffMembers.filter(staff => {
    return (
      staff.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      staff.schedule.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Staff Management</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Manage your staff's schedules, roles, and permissions.</p>
        </div>
      </div>
      
      <div className="pb-3">
        <div className="flex border-b border-[#e2dcd4] px-4 gap-8">
          <a 
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'staff-list' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`} 
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('staff-list'); }}
          >
            <p className={`${activeTab === 'staff-list' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Staff List</p>
          </a>
          <a 
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'roles' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`} 
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('roles'); }}
          >
            <p className={`${activeTab === 'roles' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Roles</p>
          </a>
          <a 
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'permissions' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`} 
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('permissions'); }}
          >
            <p className={`${activeTab === 'permissions' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Permissions</p>
          </a>
        </div>
      </div>
      
      <div className="px-4 py-3">
        <label className="flex flex-col min-w-40 h-12 w-full">
          <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
            <div
              className="text-[#8a745c] flex border-none bg-[#f1edea] items-center justify-center pl-4 rounded-l-lg border-r-0"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" fill="currentColor" viewBox="0 0 256 256">
                <path
                  d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z"
                ></path>
              </svg>
            </div>
            <input
              placeholder="Search staff"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </label>
      </div>
      
      <div className="px-4 py-3 @container">
        <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
          <table className="flex-1">
            <thead>
              <tr className="bg-[#fbfaf9]">
                <th className="table-staff-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Name</th>
                <th className="table-staff-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Role</th>
                <th className="table-staff-360 px-4 py-3 text-left text-[#181510] w-60 text-sm font-medium leading-normal">Status</th>
                <th className="table-staff-480 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">
                  Schedule
                </th>
                <th className="table-staff-600 px-4 py-3 text-left w-60 text-[#8a745c] text-sm font-medium leading-normal">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredStaff.map((staff) => (
                <tr key={staff.id} className="border-t border-t-[#e2dcd4]">
                  <td className="table-staff-120 h-[72px] px-4 py-2 w-[400px] text-[#181510] text-sm font-normal leading-normal">
                    {staff.name}
                  </td>
                  <td className="table-staff-240 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">{staff.role}</td>
                  <td className="table-staff-360 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                    <button
                      className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-8 px-4 bg-[#f1edea] text-[#181510] text-sm font-medium leading-normal w-full"
                    >
                      <span className="truncate">{staff.status === 'active' ? 'Active' : 'Inactive'}</span>
                    </button>
                  </td>
                  <td className="table-staff-480 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">
                    {staff.schedule}
                  </td>
                  <td className="table-staff-600 h-[72px] px-4 py-2 w-60 text-[#8a745c] text-sm font-bold leading-normal tracking-[0.015em]">
                    View
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <style dangerouslySetInnerHTML={{
          __html: `
            @container(max-width:120px){.table-staff-120{display: none;}}
            @container(max-width:240px){.table-staff-240{display: none;}}
            @container(max-width:360px){.table-staff-360{display: none;}}
            @container(max-width:480px){.table-staff-480{display: none;}}
            @container(max-width:600px){.table-staff-600{display: none;}}
          `
        }} />
      </div>
      
      <div className="flex px-4 py-3 justify-end">
        <button
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#e5ccb2] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em]"
        >
          <span className="truncate">Add Staff</span>
        </button>
      </div>
    </>
  );
}
