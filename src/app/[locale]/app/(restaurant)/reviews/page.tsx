'use client';

import { useState } from 'react';
import { useGetReviewsQuery, useRespondToReviewMutation, useUpdateReviewStatusMutation } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StarRating } from '@/components/ui/star-rating';
import ReviewCard from '@/components/restaurant/ReviewCard';
import { toast } from 'sonner';
import mockReviews from '@/mock/reviewData';

export default function ReviewsPage() {
  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');

  // Use mock data for now, but in a real app we would use the API
  // const { data: reviews, isLoading, refetch } = useGetReviewsQuery({ merchantId });
  // const [respondToReview, { isLoading: isResponding }] = useRespondToReviewMutation();
  // const [updateReviewStatus, { isLoading: isUpdating }] = useUpdateReviewStatusMutation();
  const [reviews, setReviews] = useState(mockReviews);
  const isLoading = false;
  const isResponding = false;
  const isUpdating = false;

  // Filter reviews based on search term, rating, and tab
  const filteredReviews = reviews
    .filter(review => {
      // Filter by search term
      if (searchTerm && !review.content.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !review.reviewer.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by rating
      if (ratingFilter && ratingFilter !== 'all' && review.rating !== parseInt(ratingFilter)) {
        return false;
      }

      // Filter by tab (status)
      if (activeTab !== 'all' && review.status !== activeTab) {
        return false;
      }

      return true;
    })
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // Handle responding to a review
  const handleRespond = (reviewId: string) => {
    setSelectedReviewId(reviewId);
    setResponseText('');
    setIsRespondDialogOpen(true);
  };

  // Submit response to a review
  const submitResponse = async () => {
    if (!selectedReviewId || !responseText.trim()) return;

    try {
      // In a real app, we would call the API
      // await respondToReview({
      //   merchantId,
      //   reviewId: selectedReviewId,
      //   response: responseText.trim()
      // }).unwrap();

      // Update the mock data
      setReviews(reviews.map(review =>
        review.id === selectedReviewId
          ? { ...review, response: responseText.trim() }
          : review
      ));

      toast.success('Response submitted successfully');
      setIsRespondDialogOpen(false);
      setSelectedReviewId(null);
      setResponseText('');
    } catch (error) {
      toast.error('Failed to submit response');
      console.error('Error responding to review:', error);
    }
  };

  // Handle updating review status
  const handleUpdateStatus = async (reviewId: string, status: string) => {
    try {
      // In a real app, we would call the API
      // await updateReviewStatus({ merchantId, reviewId, status }).unwrap();

      // Update the mock data
      setReviews(reviews.map(review =>
        review.id === reviewId ? { ...review, status } : review
      ));

      toast.success(`Review ${status} successfully`);
    } catch (error) {
      toast.error(`Failed to update review status`);
      console.error('Error updating review status:', error);
    }
  };

  // Get the selected review
  const selectedReview = selectedReviewId
    ? reviews.find(review => review.id === selectedReviewId)
    : null;

  return (
    <div className="p-6 font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Reviews</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">Manage and respond to customer reviews</p>
        </div>
        <div className="flex items-end gap-3">
          <div className="flex flex-col gap-1.5">
            <label htmlFor="rating-filter" className="text-xs font-medium text-[#8a745c]">
              Filter by Rating
            </label>
            <Select value={ratingFilter || 'all'} onValueChange={setRatingFilter}>
              <SelectTrigger id="rating-filter" className="w-[120px] bg-[#fbfaf9] border-[#e5e1dc]">
                <SelectValue placeholder="All Ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex-1 min-w-[200px]">
            <Input
              placeholder="Search reviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="bg-[#fbfaf9] border-[#e5e1dc]"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="bg-[#f1edea] p-1">
          <TabsTrigger value="all" className="data-[state=active]:bg-[#fbfaf9]">
            All Reviews
          </TabsTrigger>
          <TabsTrigger value="published" className="data-[state=active]:bg-[#fbfaf9]">
            Published
          </TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:bg-[#fbfaf9]">
            Pending
          </TabsTrigger>
          <TabsTrigger value="flagged" className="data-[state=active]:bg-[#fbfaf9]">
            Flagged
          </TabsTrigger>
          <TabsTrigger value="archived" className="data-[state=active]:bg-[#fbfaf9]">
            Archived
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Reviews List */}
      {isLoading ? (
        <div className="text-center py-10">Loading reviews...</div>
      ) : filteredReviews.length === 0 ? (
        <div className="text-center py-10 text-[#8a745c]">
          No reviews found. Adjust your filters to see more reviews.
        </div>
      ) : (
        <div className="space-y-4">
          {filteredReviews.map((review) => (
            <ReviewCard
              key={review.id}
              review={review}
              onRespond={handleRespond}
              onUpdateStatus={handleUpdateStatus}
            />
          ))}
        </div>
      )}

      {/* Respond Dialog */}
      <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e5e1dc] w-[95%] max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-[#181510]">Respond to Review</DialogTitle>
          </DialogHeader>

          {selectedReview && (
            <div className="py-4">
              <div className="flex items-center gap-2 mb-2">
                <div className="font-medium text-[#181510]">{selectedReview.reviewer.name}</div>
                <div className="flex items-center">
                  <StarRating rating={selectedReview.rating} size="sm" />
                  <span className="text-sm ml-1">{selectedReview.rating}</span>
                </div>
              </div>
              <p className="text-[#181510] text-sm mb-4">{selectedReview.content}</p>

              <div className="space-y-2">
                <label htmlFor="response" className="text-sm font-medium text-[#8a745c]">
                  Your Response
                </label>
                <Textarea
                  id="response"
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                  placeholder="Type your response here..."
                  className="bg-white border-[#e5e1dc] min-h-[100px]"
                />
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRespondDialogOpen(false)}
              className="border-[#e5e1dc]"
            >
              Cancel
            </Button>
            <Button
              onClick={submitResponse}
              disabled={!responseText.trim() || isResponding}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
            >
              {isResponding ? 'Submitting...' : 'Submit Response'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
