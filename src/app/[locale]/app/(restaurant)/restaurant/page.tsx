'use client';

import { useTranslations } from 'next-intl';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { AppLoading } from '@/components/ui/app-loading';

// Mock restaurant data
const initialRestaurantData = {
  name: 'Thai Delight Restaurant',
  description: 'Authentic Thai cuisine in a cozy atmosphere',
  address: '123 Main Street, Bangkok, Thailand',
  phone: '+66 2 123 4567',
  email: '<EMAIL>',
  openingHours: 'Mon-Fri: 10:00 AM - 10:00 PM, Sat-Sun: 11:00 AM - 11:00 PM',
  cuisine: 'Thai',
};

// Mock menu items
const initialMenuItems = [
  {
    id: '1',
    name: 'Pad Thai',
    description: 'Classic Thai stir-fried noodles with tofu, bean sprouts, and peanuts',
    price: 120,
    category: 'mainCourse',
    image: 'https://via.placeholder.com/150',
    available: true,
    popular: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    description: 'Spicy and sour shrimp soup with lemongrass and lime leaves',
    price: 150,
    category: 'appetizer',
    image: 'https://via.placeholder.com/150',
    available: true,
    popular: true,
  },
  {
    id: '3',
    name: 'Green Curry',
    description: 'Spicy green curry with coconut milk, vegetables, and chicken',
    price: 140,
    category: 'mainCourse',
    image: 'https://via.placeholder.com/150',
    available: true,
    popular: false,
  },
  {
    id: '4',
    name: 'Mango Sticky Rice',
    description: 'Sweet sticky rice with fresh mango and coconut milk',
    price: 90,
    category: 'dessert',
    image: 'https://via.placeholder.com/150',
    available: true,
    popular: true,
  },
];

export default function RestaurantPage() {
  const t = useTranslations('restaurant');
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('info');
  const [restaurantData, setRestaurantData] = useState(initialRestaurantData);
  const [menuItems, setMenuItems] = useState(initialMenuItems);
  const [editingMenuItem, setEditingMenuItem] = useState<any>(null);
  const [isAddingMenuItem, setIsAddingMenuItem] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Redirect to tables page
  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
      router.push('/app/restaurant/tables');
    }, 2000);

    return () => clearTimeout(timer);
  }, [router]);

  // Handle restaurant info form submission
  const handleInfoSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the data to the backend here
    alert('Restaurant information saved successfully!');
  };

  // Handle menu item form submission
  const handleMenuItemSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isAddingMenuItem) {
      // Add new menu item
      const newItem = {
        ...editingMenuItem,
        id: (menuItems.length + 1).toString(),
      };
      setMenuItems([...menuItems, newItem]);
      setIsAddingMenuItem(false);
    } else {
      // Update existing menu item
      const updatedItems = menuItems.map(item =>
        item.id === editingMenuItem.id ? editingMenuItem : item
      );
      setMenuItems(updatedItems);
    }
    setEditingMenuItem(null);
  };

  // Handle menu item deletion
  const handleDeleteMenuItem = (id: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this menu item?');
    if (confirmed) {
      setMenuItems(menuItems.filter(item => item.id !== id));
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('title')}</h1>
        <Link href="/app" className="text-[#8a745c] hover:text-[#181510] flex items-center">
          <span className="material-icons mr-1">arrow_back</span>
          Back to Dashboard
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow">
        {/* Tabs */}
        <div className="border-b">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('info')}
              className={`px-4 py-3 text-sm font-medium ${
                activeTab === 'info'
                  ? 'border-b-2 border-[#8a745c] text-[#8a745c]'
                  : 'text-[#181510] hover:text-[#8a745c]'
              }`}
            >
              {t('info.title')}
            </button>
            <button
              onClick={() => setActiveTab('menu')}
              className={`px-4 py-3 text-sm font-medium ${
                activeTab === 'menu'
                  ? 'border-b-2 border-[#8a745c] text-[#8a745c]'
                  : 'text-[#181510] hover:text-[#8a745c]'
              }`}
            >
              {t('menu.title')}
            </button>
          </nav>
        </div>

        {/* Restaurant Info Tab */}
        {activeTab === 'info' && (
          <div className="p-6">
            <form onSubmit={handleInfoSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.name')}
                  </label>
                  <input
                    type="text"
                    value={restaurantData.name}
                    onChange={(e) => setRestaurantData({...restaurantData, name: e.target.value})}
                    className="w-full p-2 border rounded-md"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.cuisine')}
                  </label>
                  <input
                    type="text"
                    value={restaurantData.cuisine}
                    onChange={(e) => setRestaurantData({...restaurantData, cuisine: e.target.value})}
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.description')}
                  </label>
                  <textarea
                    value={restaurantData.description}
                    onChange={(e) => setRestaurantData({...restaurantData, description: e.target.value})}
                    className="w-full p-2 border rounded-md"
                    rows={3}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.address')}
                  </label>
                  <textarea
                    value={restaurantData.address}
                    onChange={(e) => setRestaurantData({...restaurantData, address: e.target.value})}
                    className="w-full p-2 border rounded-md"
                    rows={2}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.phone')}
                  </label>
                  <input
                    type="text"
                    value={restaurantData.phone}
                    onChange={(e) => setRestaurantData({...restaurantData, phone: e.target.value})}
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.email')}
                  </label>
                  <input
                    type="email"
                    value={restaurantData.email}
                    onChange={(e) => setRestaurantData({...restaurantData, email: e.target.value})}
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    {t('info.openingHours')}
                  </label>
                  <textarea
                    value={restaurantData.openingHours}
                    onChange={(e) => setRestaurantData({...restaurantData, openingHours: e.target.value})}
                    className="w-full p-2 border rounded-md"
                    rows={2}
                  />
                </div>
              </div>

              <div className="mt-6">
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#8a745c] text-white rounded-md hover:bg-[#6d5c49]"
                >
                  {t('info.save')}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Menu Management Tab */}
        {activeTab === 'menu' && (
          <div className="p-6">
            {!editingMenuItem ? (
              <>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">{t('menu.title')}</h2>
                  <button
                    onClick={() => {
                      setEditingMenuItem({
                        name: '',
                        description: '',
                        price: 0,
                        category: 'mainCourse',
                        image: 'https://via.placeholder.com/150',
                        available: true,
                        popular: false,
                      });
                      setIsAddingMenuItem(true);
                    }}
                    className="px-3 py-1 bg-[#8a745c] text-white rounded-md hover:bg-[#6d5c49] flex items-center"
                  >
                    <span className="material-icons mr-1">add</span>
                    {t('menu.addItem')}
                  </button>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-[#fbfaf9]">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          {t('menu.name')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          {t('menu.category')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          {t('menu.price')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          {t('menu.available')}
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          {t('menu.popular')}
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-[#8a745c] uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-[#f1edea]">
                      {menuItems.map((item) => (
                        <tr key={item.id} className="hover:bg-[#fbfaf9]">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10">
                                <img className="h-10 w-10 rounded-full" src={item.image} alt={item.name} />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-[#181510]">{item.name}</div>
                                <div className="text-sm text-[#8a745c] truncate max-w-xs">{item.description}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-[#181510]">
                              {t(`menu.categories.${item.category}`)}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-[#181510]">฿{item.price}</span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {item.available ? 'Yes' : 'No'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              item.popular ? 'bg-[#f1edea] text-[#8a745c]' : 'bg-gray-100 text-gray-800'
                            }`}>
                              {item.popular ? 'Yes' : 'No'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => {
                                setEditingMenuItem(item);
                                setIsAddingMenuItem(false);
                              }}
                              className="text-[#8a745c] hover:text-[#181510] mr-3"
                            >
                              Edit
                            </button>
                            <button
                              onClick={() => handleDeleteMenuItem(item.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </>
            ) : (
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold">
                    {isAddingMenuItem ? t('menu.addItem') : t('menu.editItem')}
                  </h2>
                  <button
                    onClick={() => setEditingMenuItem(null)}
                    className="text-[#8a745c] hover:text-[#181510]"
                  >
                    <span className="material-icons">close</span>
                  </button>
                </div>

                <form onSubmit={handleMenuItemSubmit}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-1">
                        {t('menu.name')}
                      </label>
                      <input
                        type="text"
                        value={editingMenuItem.name}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, name: e.target.value})}
                        className="w-full p-2 border rounded-md"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-1">
                        {t('menu.price')}
                      </label>
                      <input
                        type="number"
                        value={editingMenuItem.price}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, price: Number(e.target.value)})}
                        className="w-full p-2 border rounded-md"
                        required
                        min="0"
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-[#181510] mb-1">
                        {t('menu.description')}
                      </label>
                      <textarea
                        value={editingMenuItem.description}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, description: e.target.value})}
                        className="w-full p-2 border rounded-md"
                        rows={3}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-1">
                        {t('menu.category')}
                      </label>
                      <select
                        value={editingMenuItem.category}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, category: e.target.value})}
                        className="w-full p-2 border rounded-md"
                      >
                        <option value="appetizer">{t('menu.categories.appetizer')}</option>
                        <option value="mainCourse">{t('menu.categories.mainCourse')}</option>
                        <option value="dessert">{t('menu.categories.dessert')}</option>
                        <option value="beverage">{t('menu.categories.beverage')}</option>
                        <option value="special">{t('menu.categories.special')}</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-1">
                        {t('menu.image')}
                      </label>
                      <input
                        type="text"
                        value={editingMenuItem.image}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, image: e.target.value})}
                        className="w-full p-2 border rounded-md"
                      />
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="available"
                        checked={editingMenuItem.available}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, available: e.target.checked})}
                        className="h-4 w-4 text-[#8a745c] border-[#e6e0da] rounded"
                      />
                      <label htmlFor="available" className="ml-2 block text-sm text-[#181510]">
                        {t('menu.available')}
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="popular"
                        checked={editingMenuItem.popular}
                        onChange={(e) => setEditingMenuItem({...editingMenuItem, popular: e.target.checked})}
                        className="h-4 w-4 text-[#8a745c] border-[#e6e0da] rounded"
                      />
                      <label htmlFor="popular" className="ml-2 block text-sm text-[#181510]">
                        {t('menu.popular')}
                      </label>
                    </div>
                  </div>

                  <div className="mt-6 flex space-x-3">
                    <button
                      type="submit"
                      className="px-4 py-2 bg-[#8a745c] text-white rounded-md hover:bg-[#6d5c49]"
                    >
                      {t('menu.save')}
                    </button>
                    <button
                      type="button"
                      onClick={() => setEditingMenuItem(null)}
                      className="px-4 py-2 bg-[#f1edea] text-[#181510] rounded-md hover:bg-[#e6e0da]"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
