'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { useGetActiveOrdersQuery, useGetCompletedOrdersQuery } from '@/lib/redux/api/endpoints/ordersApi';
import { format } from 'date-fns';

// Fallback mock data for active orders
const mockActiveOrders = [
  {
    id: '#12345',
    customer: { name: '<PERSON>' },
    items: [
      { name: 'Margherita Pizza', quantity: 2 },
      { name: 'Caesar Salad', quantity: 1 }
    ],
    status: 'preparing',
    createdAt: new Date().toISOString(),
  },
  {
    id: '#12346',
    customer: { name: '<PERSON>' },
    items: [
      { name: 'Pasta Carbonara', quantity: 1 },
      { name: 'Garlic Bread', quantity: 1 }
    ],
    status: 'preparing',
    createdAt: new Date().toISOString(),
  },
  {
    id: '#12347',
    customer: { name: '<PERSON>' },
    items: [
      { name: '<PERSON> Sandwich', quantity: 1 },
      { name: 'Fries', quantity: 1 }
    ],
    status: 'preparing',
    createdAt: new Date().toISOString(),
  },
];

// Fallback mock data for completed orders
const mockCompletedOrders = [
  {
    id: '#12344',
    customer: { name: '<PERSON>' },
    items: [
      { name: 'Burger', quantity: 1 },
      { name: 'Onion Rings', quantity: 1 }
    ],
    status: 'completed',
    createdAt: new Date(Date.now() - 20 * 60000).toISOString(),
    completedAt: new Date(Date.now() - 10 * 60000).toISOString(),
  },
  {
    id: '#12343',
    customer: { name: 'Liam Wilson' },
    items: [
      { name: 'Steak', quantity: 1 },
      { name: 'Mashed Potatoes', quantity: 1 }
    ],
    status: 'completed',
    createdAt: new Date(Date.now() - 35 * 60000).toISOString(),
    completedAt: new Date(Date.now() - 15 * 60000).toISOString(),
  },
];

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState('active');

  // In a real app, we would get the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // Fetch orders data
  const { data: activeOrders = mockActiveOrders, isLoading: isLoadingActive } = useGetActiveOrdersQuery(merchantId);
  const { data: completedOrders = mockCompletedOrders, isLoading: isLoadingCompleted } = useGetCompletedOrdersQuery(merchantId);

  // Format items for display
  const formatItems = (items) => {
    return items.map(item => `${item.quantity}x ${item.name}`).join(', ');
  };

  // Format time for display
  const formatTime = (dateString) => {
    if (!dateString) return '';
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">Orders</p>
          <p className="text-[#81766a] text-sm font-normal leading-normal">Manage incoming orders and track their progress.</p>
        </div>
      </div>

      <div className="pb-3">
        <div className="flex border-b border-[#e3e1dd] px-4 gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'all'
                ? 'border-b-[#161412] text-[#161412]'
                : 'border-b-transparent text-[#81766a]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('all');
            }}
          >
            <p className={`${
              activeTab === 'all'
                ? 'text-[#161412]'
                : 'text-[#81766a]'
            } text-sm font-bold leading-normal tracking-[0.015em]`}>All Orders</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'active'
                ? 'border-b-[#161412] text-[#161412]'
                : 'border-b-transparent text-[#81766a]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('active');
            }}
          >
            <p className={`${
              activeTab === 'active'
                ? 'text-[#161412]'
                : 'text-[#81766a]'
            } text-sm font-bold leading-normal tracking-[0.015em]`}>Active Orders</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${
              activeTab === 'completed'
                ? 'border-b-[#161412] text-[#161412]'
                : 'border-b-transparent text-[#81766a]'
            } pb-[13px] pt-4`}
            href="#"
            onClick={(e) => {
              e.preventDefault();
              setActiveTab('completed');
            }}
          >
            <p className={`${
              activeTab === 'completed'
                ? 'text-[#161412]'
                : 'text-[#81766a]'
            } text-sm font-bold leading-normal tracking-[0.015em]`}>Completed Orders</p>
          </a>
        </div>
      </div>

      {/* Active Orders Section */}
      {(activeTab === 'all' || activeTab === 'active') && (
        <>
          <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Active Orders</h3>
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
              <table className="flex-1">
                <thead>
                  <tr className="bg-white">
                    <th className="table-active-column-120 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Order ID
                    </th>
                    <th className="table-active-column-240 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Customer
                    </th>
                    <th className="table-active-column-360 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Items
                    </th>
                    <th className="table-active-column-480 px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">
                      Status
                    </th>
                    <th className="table-active-column-600 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Time Placed
                    </th>
                    <th className="table-active-column-720 px-4 py-3 text-left w-60 text-[#81766a] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {activeOrders.map((order) => (
                    <tr key={order.id} className="border-t border-t-[#e3e1dd]">
                      <td className="table-active-column-120 h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">
                        {order.id}
                      </td>
                      <td className="table-active-column-240 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {order.customer.name}
                      </td>
                      <td className="table-active-column-360 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {formatItems(order.items)}
                      </td>
                      <td className="table-active-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <div className="flex min-w-[84px] max-w-[480px] items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-full">
                          <span className="truncate">{order.status.charAt(0).toUpperCase() + order.status.slice(1)}</span>
                        </div>
                      </td>
                      <td className="table-active-column-600 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {formatTime(order.createdAt)}
                      </td>
                      <td className="table-active-column-720 h-[72px] px-4 py-2 w-60 text-[#81766a] text-sm font-bold leading-normal tracking-[0.015em]">
                        <Link href={`/app/restaurant/orders/${order.id.replace('#', '')}`} className="hover:text-[#161412]">
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style>
              {`
                @container(max-width:120px){.table-active-column-120{display: none;}}
                @container(max-width:240px){.table-active-column-240{display: none;}}
                @container(max-width:360px){.table-active-column-360{display: none;}}
                @container(max-width:480px){.table-active-column-480{display: none;}}
                @container(max-width:600px){.table-active-column-600{display: none;}}
                @container(max-width:720px){.table-active-column-720{display: none;}}
              `}
            </style>
          </div>
        </>
      )}

      {/* Completed Orders Section */}
      {(activeTab === 'all' || activeTab === 'completed') && (
        <>
          <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Completed Orders</h3>
          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
              <table className="flex-1">
                <thead>
                  <tr className="bg-white">
                    <th className="table-completed-column-120 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Order ID
                    </th>
                    <th className="table-completed-column-240 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Customer
                    </th>
                    <th className="table-completed-column-360 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Items
                    </th>
                    <th className="table-completed-column-480 px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">
                      Status
                    </th>
                    <th className="table-completed-column-600 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Time Placed
                    </th>
                    <th className="table-completed-column-720 px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">
                      Time Completed
                    </th>
                    <th className="table-completed-column-840 px-4 py-3 text-left w-60 text-[#81766a] text-sm font-medium leading-normal">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {completedOrders.map((order) => (
                    <tr key={order.id} className="border-t border-t-[#e3e1dd]">
                      <td className="table-completed-column-120 h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">
                        {order.id}
                      </td>
                      <td className="table-completed-column-240 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {order.customer.name}
                      </td>
                      <td className="table-completed-column-360 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {formatItems(order.items)}
                      </td>
                      <td className="table-completed-column-480 h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                        <div className="flex min-w-[84px] max-w-[480px] items-center justify-center overflow-hidden rounded-full h-8 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-medium leading-normal w-full">
                          <span className="truncate">{order.status.charAt(0).toUpperCase() + order.status.slice(1)}</span>
                        </div>
                      </td>
                      <td className="table-completed-column-600 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {formatTime(order.createdAt)}
                      </td>
                      <td className="table-completed-column-720 h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">
                        {formatTime(order.completedAt)}
                      </td>
                      <td className="table-completed-column-840 h-[72px] px-4 py-2 w-60 text-[#81766a] text-sm font-bold leading-normal tracking-[0.015em]">
                        <Link href={`/app/restaurant/orders/${order.id.replace('#', '')}`} className="hover:text-[#161412]">
                          View
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style>
              {`
                @container(max-width:120px){.table-completed-column-120{display: none;}}
                @container(max-width:240px){.table-completed-column-240{display: none;}}
                @container(max-width:360px){.table-completed-column-360{display: none;}}
                @container(max-width:480px){.table-completed-column-480{display: none;}}
                @container(max-width:600px){.table-completed-column-600{display: none;}}
                @container(max-width:720px){.table-completed-column-720{display: none;}}
                @container(max-width:840px){.table-completed-column-840{display: none;}}
              `}
            </style>
          </div>
        </>
      )}
    </>
  );
}
