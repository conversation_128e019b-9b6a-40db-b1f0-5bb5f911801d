'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useGetTablesQuery } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ReservationForm from '@/components/restaurant/ReservationForm';
import ReservationTab from '@/components/restaurant/ReservationTab';

// Mock data for tables (will be replaced with real data from API)
const mockTables = [
  {
    id: '1',
    number: 1,
    capacity: 4,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBwbjW6nxCuPdBRW9fDC5h5CeYIO0b4g3pi0JP4jRZ91Lg4NgJSwevVyP3K5Zc4uzYWXPMlL1Ru_lerFb8NRTn1gDYYrnwqzkZq0ZH_h1It93buszWUF0m6kbL9CDZozFGcSofl9DfHuQPtpft3mFOxS5YU3DPOwfmTcBbCOoR9kU_UW4yOrYFt4TOiOmvZHXdjuyvrYTmr7mCkTKT-uY1ittIruQ4GTqvXM7E-p_ZrjGOaaG7J-sr3_3e2AQAeLIpwLfqRKloSg7V'
  },
  {
    id: '2',
    number: 2,
    capacity: 2,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCm5kKI8V-CR8Cq_8PQH4XUAiPQirzRWlhKic0WW1RETxcRK-_Ar3CJjWXGUmL7zwdy0fhyEb6uKSin4etsZO72RQ-9anPe5RnTyJSuMSPntdFdejZEVZCTQ_of2HgVS4wiqf8wkmwzT7PbrONDjcdli0eXZURLYp0iRgXrSndH8i-wLDieR9ZonyQMrMiOd0zVNzsV1Lf0lHlTAcamZTZmhz66EcDm0Y-LT-gU29Liy1qhDb3ykMOCMrobBocxOy1uLa8lhYkp8qSj'
  },
  {
    id: '3',
    number: 3,
    capacity: 6,
    status: 'occupied',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmluEqeoS8maaJInF5ENft68j3Zo6lrjuN6Cz7EX7tmiurjpgiPC9qFmQ7ouTL5CYHEClO4_K_Gm8g1Ez2N5hvb-EnIEFj6yrbX9nR6oPzI7jpKCsiZ4uhOV0-0s_7FYdmG_jOntuziVZiVzaA7k34MrhfEHBiKeCWcrS_eGHx_0hIdpIeWlTAgRvihrqcs4H0aBkPRqRnQ-JoHezoQJkBq1MANw1b93TUOfADf3916wSitfLZ5S0LOvSeK3SOJBSoTJc8gV0LiQOV'
  },
  {
    id: '4',
    number: 4,
    capacity: 4,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDeKZLfgmskuVwyKS6M2mqm-wHKoVsx8DUwJ6orJ3HRCmqVzBBNb18hK6B3-rHkTmyFU9u8I67VNnvnp8zp-7hjz2q2sw57FOn4Xg9fWvMpmBSfcyogsig7XCGMWkWIsSw9QkY275_LLly2gW72XfWBGIOXvA9IYAg3lhMMczeNRmF5CKp7-kNIRydPwoUlRyFJzDLnsznrY0rgP3JsjxN775-duLZB5pHV26d8CEz-9Kybxe25OBNE9Lr--B8ghsYf4AYEgSA1NpoX'
  },
  {
    id: '5',
    number: 5,
    capacity: 2,
    status: 'reserved',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBaYcUah5GnpaX-LjIAnsnx7b_FGm530dKhaX9qcIR5xbMeZb5Jb4PTg2Mqiz5mheEjIXkdPnwWHbYeoeyRYeKxxhlxkQo9iflGzKG4oD2YuzuONa-DMQBc4F9iDEfO7F_AqIZ6Hb4nyc7J7ATNBgcrfduzhToQ5uH7YMzcAe-yNk7K5TiQAJxiSJY_EhzZTlliY7ThA4olnMU74LqlKQ9Q4lKXSgUdtLGqwf5n05eG84-4F4qcmFrkvwv-SPac7NG4U6KgcIkdXR7U'
  },
  {
    id: '6',
    number: 6,
    capacity: 8,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuC3UPgd7rhRsDT7eYfAD2T-sPufNEqRrHt5F6dHFgUXuNokqidPvPIdhj3XI4faNbEaoQlvdtuFn-U7t4dkbq_RrUyNsUkfgNJKCNFnixLjqvF5_7YNHuqbYpeBlzoY6EK4p9pyznahnSoHr8XMpikZC6_g1xVmvHna5tEadf1ov8--gq51t7rzp6KSCrCsgdx54NAfSyPmCmhNYUS0OAmH5zuk7wG3Y6OAKzF-Hukya2ehV8z7_1fMbUH4yNw7dugwyru9is9ljIPo'
  },
  {
    id: '7',
    number: 7,
    capacity: 4,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuD9HQ9qBH6E7N3BPxRfx1bJXYd-TZZ_6SLmdly5C6k5wNtgkYmpZG06l39IEz_Ia2tiCQi5BdrFigYqg8Gpq1-xI1WgDzGmg0Qat3AfwRURE5qm-65to1Zi0EgrXtBdCYGw5g1s3l2NWWfQer0oRvEIDKXnmBOeO_CecYUgeei8goDhIbiUOuPqv-UNzHQ1B2o_WHSFIi-SHIgK71I9qBSRNkJprUpW8Xk75UNFiMhj8zZT8o1YWmVw-yeT7RkO3KnNzBXDBfMrjPGk'
  },
  {
    id: '8',
    number: 8,
    capacity: 6,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAbEbLgXmmGsSE-rc6BgB_EHvxJYYfk_q1Q6LdxMjBBZusJOu-SR5Lj9U-j1thggcac0GlBkzjriHuqAJT8rU1zn0t6rhOecLtktvA-gqb__BzGsL4DPIDmo66KCH7G_kt8dpUhYOYQhCgBJVcpHR2bqnsGYutNIJydMCAbtVyRHMFMhaMmLqAnywhsFVUeh1wyjBNKXhw8DSNRkWVYCF3b_zUoQhGV-Y1xeEwwJ3uJGuTJanIbsc8W4lr-_fMslYcr_s7uWXg6_jRB'
  },
  {
    id: '9',
    number: 9,
    capacity: 4,
    status: 'available',
    location: 'dining',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCIz0HXWiDXpcBAoN5tkRxMfmuAZEr_bR5i1v-LJGz3rJ3bvTCxwd8-zdvsbOZZ846ZRH69lPKTQiW5NhJbP_-e3T_xnLqjmYDVrm43IqtOt6vDblBqxeC5_gYUT2gwS-pdLP3TnRxdGJIHEbW3ExL4uUJW4Em8SGOCPccBfy_FSmLzYZDwWJ_GojPr5zBp0JbwDDs2nR2aN2eiI385VFdl4nezOKcrRdmm1PrhMYjFoOHAE5eczLj91yOzHwZbmIgJJK7l5xSXD7eC'
  },
  {
    id: '10',
    number: 10,
    capacity: 2,
    status: 'available',
    location: 'outdoor',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBOGlr9W9KBrS6VSv8V5HCE6ylasPrD1NvE3mMs9AB2hhRc-2lZbrQivuaDLwLoe2I9mGFwJ1LFXVof7D8u-eu_9l3XGCfh4y2Sbi4OA-hdCU45bq0R7yRdaavSiIuVcseKMzxbcJ8q6Q482YXBk2DqSLE0Zi34Ylbbdc-mhH0QwXgIQXlS55M3aporIXMXJQ_-87FmDSUwDZ2jB04dihpgs-zOGDxvIj9qWKdGNNzBkvgy3TjFzGDAExf_Dic6ZUJQdnAAvcxTc6j'
  },
  {
    id: '11',
    number: 11,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDQKknOb2zf7QS-q5HVjzt6C9rVLYuiEImsgWF3cHQ1DIe3pwEDkS52hck_SgugsNk5kbogoXGY-MZpIZF2dGDZzE2m701-wJWBM6U-uZbGAKdxUsd2hSKZ1pSk195CtMz850v40dZ0z5TPpuOQVWfwrE4RkYji0Jaf7G6cxE02JCooay55mNHT28dHhBT0NisuKVZ0lDnd7juDMYSTttW2hEYNGt7yffWfjwZYeVVyEJlyH2XhBE8p90m1aftxmiNF5FKyOazJyKRc'
  },
  {
    id: '12',
    number: 12,
    capacity: 6,
    status: 'occupied',
    location: 'outdoor',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBPOpsvPGLf0WjPng7RmEMaYXivOfOM2lai3svFLXM04vqptsNAX9uxiWvS-o_NEOdNNIaKb2DKm75VHtfI3r9zTGyXzgXxEzXohtWdRDak4gLqLk548iHHZqPMFKqTLTxVXcmc6uNgV2-y9RuxQJn4pIMflICpykz44_m7-cTlT-EgieVnPwgCOTLvyUxfsrckXlbi8Cdl_ycO--899rs5YXfncTlBEC3GEt5KDUCrjJyx4VJRnjWgwjwRvs6bqPmazyRTqvKIT8QB'
  },
  {
    id: '13',
    number: 13,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBYiexir2spyoRDU3_rkmQBrbjy6urJ2_sWfYE0qreHXkmTuT2tkefw9kzRmZrjD69ySwjLf33iP_H5ZqCiRDlsWX2GCYE1ISYFZ1nBl6h56DaWb_wIQJ86ClIsL5xr9IpJ45vkG4Vn8OYfXx86M2zs-R7sc-PcWx62h0HTIQYOJCTvSP9TcYFb-Ap1ASfJ98acBSq-TlqXKrIquDlTdbkkATyF_Qy71BzuRznevhEI4VrBPYzJn_HjNdIj9bPg53WJI5c78GMn86-e'
  },
];

export default function TableLayoutPage() {
  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // Use mock data for now, but in a real app we would use the API
  // const { data: tables, isLoading } = useGetTablesQuery(merchantId);
  const [tables, setTables] = useState(mockTables);
  const [isAddReservationOpen, setIsAddReservationOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('floor-plan');

  // Filter tables by location
  const diningTables = tables.filter(table => table.location === 'dining');
  const outdoorTables = tables.filter(table => table.location === 'outdoor');

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] text-[32px] font-bold leading-tight">Table Management</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Manage your restaurant's table layout and reservations.</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="floor-plan"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Floor Plan</p>
            </TabsTrigger>
            <TabsTrigger
              value="reservations"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Reservations</p>
            </TabsTrigger>
            <TabsTrigger
              value="waitlist"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Waitlist</p>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="floor-plan" className="mt-0">
          <div>
            <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Dining Area</h2>
            <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
              {diningTables.map((table) => (
                <Link
                  href={`/app/restaurant/tables/${table.id}`}
                  key={table.id}
                  className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square hover:opacity-90 transition-opacity"
                  style={{
                    backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                  }}
                >
                  <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">Table {table.number}</p>
                </Link>
              ))}
            </div>

            <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Outdoor Patio</h2>
            <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
              {outdoorTables.map((table) => (
                <Link
                  href={`/app/restaurant/tables/${table.id}`}
                  key={table.id}
                  className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square hover:opacity-90 transition-opacity"
                  style={{
                    backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                  }}
                >
                  <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">Table {table.number}</p>
                </Link>
              ))}
            </div>

            <div className="flex justify-stretch">
              <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-end">
                <Link href="/app/restaurant/tables/qr-codes">
                  <Button
                    className="bg-[#e58219] text-[#181510] hover:bg-[#d67917]"
                  >
                    Generate QR Codes
                  </Button>
                </Link>
                <Link href="/app/restaurant/tables/layout-editor">
                  <Button
                    className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4]"
                  >
                    Edit Layout
                  </Button>
                </Link>
                <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                      Add Reservation
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <DialogHeader>
                      <DialogTitle>Add New Reservation</DialogTitle>
                    </DialogHeader>
                    <ReservationForm
                      tables={tables.map(table => ({ id: table.id, number: table.number }))}
                      onSuccess={() => setIsAddReservationOpen(false)}
                      onCancel={() => setIsAddReservationOpen(false)}
                    />
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="reservations" className="mt-0">
          <ReservationTab tables={tables} />
        </TabsContent>

        <TabsContent value="waitlist" className="mt-0">
          <div className="p-4 text-center text-[#8a745c]">
            <p>Waitlist feature coming soon</p>
          </div>
        </TabsContent>
      </Tabs>
    </>
  );
}
