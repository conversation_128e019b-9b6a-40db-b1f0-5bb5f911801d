'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, MapPin, ArrowLeft } from 'lucide-react';
import { getShopBySlug } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

interface ShopPageProps {
  params: {
    slugShop: string;
  };
}

export default function ShopPage({ params }: ShopPageProps) {
  const { slugShop } = params;
  const [shop, setShop] = useState(getShopBySlug(slugShop));
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!shop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href="/app/restaurant">
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurants
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href="/app/restaurant">
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Restaurants
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">{shop.name}</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">{shop.description}</p>
        </div>
        <div className="flex items-start">
          <Link href={`/app/restaurant/${slugShop}/new-branch`}>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <PlusCircle className="h-4 w-4 mr-2" />
              Add New Branch
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {shop.branches.map((branch) => (
          <Card key={branch.id} className="overflow-hidden bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
            <CardHeader className="pb-2">
              <CardTitle className="text-[#181510] flex items-center gap-2">
                <MapPin className="h-5 w-5 text-[#8a745c]" />
                {branch.name}
              </CardTitle>
              <CardDescription className="text-[#8a745c]">{branch.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="space-y-2">
                  <div className="text-sm text-[#181510]">
                    <span className="font-medium">Address:</span> {branch.address}
                  </div>
                  <div className="text-sm text-[#181510]">
                    <span className="font-medium">Phone:</span> {branch.phoneNumber}
                  </div>
                  <div className="text-sm text-[#181510]">
                    <span className="font-medium">Email:</span> {branch.email}
                  </div>
                  <div className="text-sm text-[#181510]">
                    <span className="font-medium">Status:</span>{' '}
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      branch.status === 'active'
                        ? 'bg-green-100 text-green-800'
                        : branch.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {branch.status.charAt(0).toUpperCase() + branch.status.slice(1)}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex justify-end">
                <Link href={`/app/restaurant/${shop.slug}/${branch.slug}/dashboard`}>
                  <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                    Manage Branch
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
