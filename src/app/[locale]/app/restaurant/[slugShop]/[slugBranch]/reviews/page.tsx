'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Star, ArrowLeft } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import React from 'react';

// Mock review data
const mockReviews = [
  {
    id: 'review1',
    merchantId: 'merchant1',
    reviewer: {
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?img=1',
    },
    rating: 5,
    date: '2023-06-10T14:30:00Z',
    content: 'Amazing food and excellent service! The atmosphere was perfect for our anniversary dinner. Will definitely come back again.',
    reviewedItems: [
      { id: 'item1', name: 'Grilled Salmon', rating: 5 },
      { id: 'item2', name: 'Chocolate Lava Cake', rating: 5 },
    ],
    status: 'published',
    response: "Thank you for your kind words! We're so glad you enjoyed your anniversary dinner with us and look forward to serving you again soon.",
  },
  {
    id: 'review2',
    merchantId: 'merchant1',
    reviewer: {
      name: 'Jane Smith',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?img=2',
    },
    rating: 4,
    date: '2023-06-08T18:45:00Z',
    content: "Great food and friendly staff. The only reason I'm not giving 5 stars is because we had to wait a bit longer than expected for our food. Otherwise, everything was perfect!",
    reviewedItems: [
      { id: 'item3', name: 'Margherita Pizza', rating: 4 },
      { id: 'item4', name: 'Tiramisu', rating: 5 },
    ],
    status: 'published',
  },
  {
    id: 'review3',
    merchantId: 'merchant1',
    reviewer: {
      name: 'Michael Johnson',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?img=3',
    },
    rating: 3,
    date: '2023-06-05T20:15:00Z',
    content: 'The food was good but the service was a bit slow. The restaurant was very busy, which might explain it. I would give them another chance.',
    reviewedItems: [
      { id: 'item5', name: 'Spaghetti Carbonara', rating: 4 },
      { id: 'item6', name: 'Garlic Bread', rating: 2 },
    ],
    status: 'published',
    response: "Thank you for your feedback. We apologize for the slow service during your visit. We're working on improving our efficiency during peak hours. We hope you'll give us another chance to serve you better!",
  },
  {
    id: 'review4',
    merchantId: 'merchant1',
    reviewer: {
      name: 'Emily Wilson',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?img=4',
    },
    rating: 5,
    date: '2023-06-03T19:30:00Z',
    content: "Absolutely loved everything about this place! The food was delicious, the staff was attentive, and the ambiance was perfect. Can't wait to come back!",
    reviewedItems: [
      { id: 'item7', name: 'Filet Mignon', rating: 5 },
      { id: 'item8', name: 'Crème Brûlée', rating: 5 },
    ],
    status: 'published',
  },
  {
    id: 'review5',
    merchantId: 'merchant1',
    reviewer: {
      name: 'David Brown',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?img=5',
    },
    rating: 2,
    date: '2023-06-01T21:00:00Z',
    content: "Disappointing experience. The food was overpriced for the quality, and our server seemed disinterested. Not sure if I would return.",
    reviewedItems: [
      { id: 'item9', name: 'Ribeye Steak', rating: 2 },
      { id: 'item10', name: 'Cheesecake', rating: 3 },
    ],
    status: 'pending',
  },
];

interface ReviewsPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function ReviewsPage({ params }: ReviewsPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');

  // Use mock data for now, but in a real app we would use the API
  const [reviews, setReviews] = useState(mockReviews);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter reviews based on search term, rating, and tab
  const filteredReviews = reviews.filter(review => {
    // Filter by search term (reviewer name or content)
    const matchesSearch = searchTerm === '' ||
      review.reviewer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.content.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by rating
    const matchesRating = ratingFilter === null || review.rating.toString() === ratingFilter;

    // Filter by tab (status)
    const matchesTab = activeTab === 'all' ||
      (activeTab === 'published' && review.status === 'published') ||
      (activeTab === 'pending' && review.status === 'pending') ||
      (activeTab === 'flagged' && review.status === 'flagged');

    return matchesSearch && matchesRating && matchesTab;
  });

  // Handle responding to a review
  const handleOpenRespondDialog = (reviewId: string) => {
    const review = reviews.find(r => r.id === reviewId);
    setSelectedReviewId(reviewId);
    setResponseText(review?.response || '');
    setIsRespondDialogOpen(true);
  };

  // Handle submitting a response
  const handleSubmitResponse = async () => {
    if (!selectedReviewId) return;

    try {
      // In a real app, we would call the API
      // await updateReviewResponse({ merchantId, reviewId: selectedReviewId, response: responseText }).unwrap();

      // Update the mock data
      setReviews(reviews.map(review =>
        review.id === selectedReviewId ? { ...review, response: responseText } : review
      ));

      setIsRespondDialogOpen(false);
      toast.success('Response submitted successfully');
    } catch (error) {
      toast.error('Failed to submit response');
    }
  };

  // Handle updating review status
  const handleUpdateStatus = async (reviewId: string, status: string) => {
    try {
      // In a real app, we would call the API
      // await updateReviewStatus({ merchantId, reviewId, status }).unwrap();

      // Update the mock data
      setReviews(reviews.map(review =>
        review.id === reviewId ? { ...review, status } : review
      ));

      toast.success(`Review ${status} successfully`);
    } catch (error) {
      toast.error(`Failed to update review status`);
    }
  };

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Reviews</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage and respond to customer reviews for {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end gap-3">
          <div className="flex flex-col gap-1.5">
            <label htmlFor="rating-filter" className="text-xs font-medium text-[#8a745c]">
              Filter by Rating
            </label>
            <Select value={ratingFilter || 'all'} onValueChange={setRatingFilter}>
              <SelectTrigger id="rating-filter" className="w-[120px] bg-[#fbfaf9] border-[#e5e1dc]">
                <SelectValue placeholder="All Ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-1.5">
            <label htmlFor="search-reviews" className="text-xs font-medium text-[#8a745c]">
              Search Reviews
            </label>
            <Input
              id="search-reviews"
              placeholder="Search by name or content"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[250px] bg-[#fbfaf9] border-[#e5e1dc]"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="bg-[#f1edea] p-1">
          <TabsTrigger value="all" className="data-[state=active]:bg-white">All Reviews</TabsTrigger>
          <TabsTrigger value="published" className="data-[state=active]:bg-white">Published</TabsTrigger>
          <TabsTrigger value="pending" className="data-[state=active]:bg-white">Pending</TabsTrigger>
          <TabsTrigger value="flagged" className="data-[state=active]:bg-white">Flagged</TabsTrigger>
        </TabsList>

        {/* Reviews List */}
        {filteredReviews.length === 0 ? (
          <div className="text-center py-10 text-[#8a745c]">
            No reviews found. Adjust your filters to see more reviews.
          </div>
        ) : (
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <div key={review.id} className="bg-[#fbfaf9] rounded-lg border border-[#e5e1dc] p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <img
                        src={review.reviewer.avatar}
                        alt={review.reviewer.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://via.placeholder.com/40?text=User';
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-[#181510]">{review.reviewer.name}</h3>
                      <div className="flex items-center mt-1">
                        <div className="flex mr-2">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-xs text-[#8a745c]">
                          {format(new Date(review.date), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      review.status === 'published'
                        ? 'bg-green-100 text-green-800'
                        : review.status === 'pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-[#181510]">{review.content}</p>
                </div>

                {review.reviewedItems.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-[#181510] mb-2">Reviewed Items:</h4>
                    <div className="flex flex-wrap gap-2">
                      {review.reviewedItems.map((item) => (
                        <div key={item.id} className="bg-[#f1edea] rounded-md px-3 py-1 text-sm">
                          <span className="text-[#181510]">{item.name}</span>
                          <span className="text-[#8a745c] ml-2">({item.rating} ★)</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {review.response && (
                  <div className="bg-[#f1edea] rounded-md p-4 mb-4">
                    <h4 className="text-sm font-medium text-[#181510] mb-2">Your Response:</h4>
                    <p className="text-[#8a745c] text-sm">{review.response}</p>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-[#e2dcd4]"
                    onClick={() => handleOpenRespondDialog(review.id)}
                  >
                    {review.response ? 'Edit Response' : 'Respond'}
                  </Button>

                  {review.status !== 'published' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-[#e2dcd4] text-green-600 hover:bg-green-50 hover:text-green-700"
                      onClick={() => handleUpdateStatus(review.id, 'published')}
                    >
                      Publish
                    </Button>
                  )}

                  {review.status !== 'flagged' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-[#e2dcd4] text-red-600 hover:bg-red-50 hover:text-red-700"
                      onClick={() => handleUpdateStatus(review.id, 'flagged')}
                    >
                      Flag
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Tabs>

      {/* Response Dialog */}
      <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Respond to Review</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Type your response here..."
              value={responseText}
              onChange={(e) => setResponseText(e.target.value)}
              rows={6}
              className="resize-none"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRespondDialogOpen(false)}
              className="border-[#e2dcd4]"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitResponse}
              className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            >
              Submit Response
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
