'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON> } from '@/i18n/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Search, Clock, CheckCircle, XCircle, Eye } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

interface OrdersPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

// Mock orders data
const mockActiveOrders = [
  {
    id: '#12345',
    customer: { name: '<PERSON>', phone: '(*************' },
    items: [
      { name: 'Margherita Pizza', quantity: 2, price: 12.99 },
      { name: 'Caesar Salad', quantity: 1, price: 8.99 }
    ],
    status: 'preparing',
    total: 34.97,
    orderType: 'dine-in',
    tableId: 'Table 5',
    createdAt: new Date().toISOString(),
    estimatedTime: 25,
  },
  {
    id: '#12346',
    customer: { name: 'Olivia Bennett', phone: '(*************' },
    items: [
      { name: 'Pasta Carbonara', quantity: 1, price: 14.99 },
      { name: 'Garlic Bread', quantity: 1, price: 4.99 }
    ],
    status: 'ready',
    total: 19.98,
    orderType: 'takeout',
    createdAt: new Date(Date.now() - 10 * 60000).toISOString(),
    estimatedTime: 5,
  },
  {
    id: '#12347',
    customer: { name: 'Noah Thompson', phone: '(*************' },
    items: [
      { name: 'Chicken Sandwich', quantity: 1, price: 11.99 },
      { name: 'Fries', quantity: 1, price: 3.99 }
    ],
    status: 'pending',
    total: 15.98,
    orderType: 'delivery',
    createdAt: new Date(Date.now() - 5 * 60000).toISOString(),
    estimatedTime: 30,
  },
];

const mockCompletedOrders = [
  {
    id: '#12344',
    customer: { name: 'Sophia Davis', phone: '(*************' },
    items: [
      { name: 'Burger', quantity: 1, price: 13.99 },
      { name: 'Onion Rings', quantity: 1, price: 5.99 }
    ],
    status: 'completed',
    total: 19.98,
    orderType: 'dine-in',
    tableId: 'Table 3',
    createdAt: new Date(Date.now() - 45 * 60000).toISOString(),
    completedAt: new Date(Date.now() - 30 * 60000).toISOString(),
  },
  {
    id: '#12343',
    customer: { name: 'Liam Wilson', phone: '(*************' },
    items: [
      { name: 'Steak', quantity: 1, price: 24.99 },
      { name: 'Mashed Potatoes', quantity: 1, price: 6.99 }
    ],
    status: 'completed',
    total: 31.98,
    orderType: 'dine-in',
    tableId: 'Table 8',
    createdAt: new Date(Date.now() - 75 * 60000).toISOString(),
    completedAt: new Date(Date.now() - 60 * 60000).toISOString(),
  },
];

export default function OrdersPage({ params }: OrdersPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('active');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  // Filter orders based on search term
  const filterOrders = (orders: any[]) => {
    return orders.filter(order =>
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.items.some((item: any) => item.name.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const filteredActiveOrders = filterOrders(mockActiveOrders);
  const filteredCompletedOrders = filterOrders(mockCompletedOrders);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      case 'preparing':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'ready':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'completed':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'preparing':
        return <Clock className="h-4 w-4" />;
      case 'ready':
        return <CheckCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <XCircle className="h-4 w-4" />;
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const orderTime = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    return `${hours}h ago`;
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Orders Management</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Manage orders for {shop.name} - {branch.name}
          </p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList className="bg-[#f1edea] p-1">
            <TabsTrigger value="active" className="data-[state=active]:bg-white">
              Active Orders ({filteredActiveOrders.length})
            </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:bg-white">
              Completed Orders ({filteredCompletedOrders.length})
            </TabsTrigger>
          </TabsList>

          <div className="flex-1 max-w-sm ml-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#8a745c] h-4 w-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        <TabsContent value="active" className="mt-0">
          {filteredActiveOrders.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-[#8a745c]">
              {searchTerm ? 'No active orders match your search' : 'No active orders'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredActiveOrders.map((order) => (
                <Card key={order.id} className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
                          {order.id}
                          <Badge className={getStatusColor(order.status)}>
                            {getStatusIcon(order.status)}
                            <span className="ml-1 capitalize">{order.status}</span>
                          </Badge>
                        </CardTitle>
                        <p className="text-[#8a745c] text-sm mt-1">
                          {order.customer.name} • {order.customer.phone}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-[#181510] font-bold">${order.total.toFixed(2)}</p>
                        <p className="text-[#8a745c] text-sm">{getTimeAgo(order.createdAt)}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4] capitalize">
                            {order.orderType}
                          </Badge>
                          {order.tableId && (
                            <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4]">
                              {order.tableId}
                            </Badge>
                          )}
                        </div>
                        {order.estimatedTime && (
                          <div className="flex items-center gap-1 text-[#8a745c] text-sm">
                            <Clock className="h-4 w-4" />
                            {order.estimatedTime} min
                          </div>
                        )}
                      </div>

                      <div className="space-y-1">
                        {order.items.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-[#181510]">{item.quantity}x {item.name}</span>
                            <span className="text-[#8a745c]">${(item.price * item.quantity).toFixed(2)}</span>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-end pt-2">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id.replace('#', '')}`}>
                          <Button variant="outline" size="sm" className="border-[#e2dcd4]">
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-0">
          {filteredCompletedOrders.length === 0 ? (
            <div className="flex items-center justify-center h-64 text-[#8a745c]">
              {searchTerm ? 'No completed orders match your search' : 'No completed orders'}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCompletedOrders.map((order) => (
                <Card key={order.id} className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-[#181510] text-lg flex items-center gap-2">
                          {order.id}
                          <Badge className={getStatusColor(order.status)}>
                            {getStatusIcon(order.status)}
                            <span className="ml-1 capitalize">{order.status}</span>
                          </Badge>
                        </CardTitle>
                        <p className="text-[#8a745c] text-sm mt-1">
                          {order.customer.name} • {order.customer.phone}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-[#181510] font-bold">${order.total.toFixed(2)}</p>
                        <p className="text-[#8a745c] text-sm">
                          Completed {getTimeAgo(order.completedAt || order.createdAt)}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4] capitalize">
                          {order.orderType}
                        </Badge>
                        {order.tableId && (
                          <Badge variant="outline" className="text-[#8a745c] border-[#e2dcd4]">
                            {order.tableId}
                          </Badge>
                        )}
                      </div>

                      <div className="space-y-1">
                        {order.items.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span className="text-[#181510]">{item.quantity}x {item.name}</span>
                            <span className="text-[#8a745c]">${(item.price * item.quantity).toFixed(2)}</span>
                          </div>
                        ))}
                      </div>

                      <div className="flex justify-end pt-2">
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders/${order.id.replace('#', '')}`}>
                          <Button variant="outline" size="sm" className="border-[#e2dcd4]">
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
