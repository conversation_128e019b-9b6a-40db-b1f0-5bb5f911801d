'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { Calendar, Clock, ArrowLeft, Trash2 } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

// Mock table options
const mockTableOptions = [
  { value: 'table-1', label: 'Table 1 (4 seats)' },
  { value: 'table-2', label: 'Table 2 (2 seats)' },
  { value: 'table-3', label: 'Table 3 (4 seats)' },
  { value: 'table-4', label: 'Table 4 (6 seats)' },
  { value: 'table-5', label: 'Table 5 (8 seats)' },
  { value: 'table-6', label: 'Table 6 (4 seats)' },
  { value: 'table-7', label: 'Table 7 (2 seats)' },
  { value: 'table-8', label: 'Table 8 (6 seats)' },
  { value: 'table-9', label: 'Table 9 (4 seats)' },
  { value: 'table-10', label: 'Table 10 (4 seats)' },
  { value: 'table-11', label: 'Table 11 (6 seats)' },
  { value: 'table-12', label: 'Table 12 (4 seats)' },
  { value: 'table-13', label: 'Table 13 (4 seats)' }
];

// Mock reservation data
const mockReservation = {
  id: 'res1',
  customerName: 'Sophia Clark',
  contactNumber: '+1 (555) 123-4567',
  reservationDate: '2024-01-15',
  reservationTime: '19:00',
  partySize: 4,
  tablePreference: 'table-3',
  status: 'Confirmed',
  notes: 'Birthday celebration'
};

interface EditReservationPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    id: string;
  }>;
}

export default function EditReservationPage({ params }: EditReservationPageProps) {
  const { slugShop, slugBranch, id } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState({
    customerName: '',
    contactNumber: '',
    reservationDate: '',
    reservationTime: '',
    partySize: '',
    tablePreference: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Load reservation data
  useEffect(() => {
    const timer = setTimeout(() => {
      // Simulate loading reservation data
      setFormData({
        customerName: mockReservation.customerName,
        contactNumber: mockReservation.contactNumber,
        reservationDate: mockReservation.reservationDate,
        reservationTime: mockReservation.reservationTime,
        partySize: mockReservation.partySize.toString(),
        tablePreference: mockReservation.tablePreference
      });
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181511] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#887663] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      alert('Reservation updated successfully!');
      setIsSubmitting(false);
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/tables`);
    }, 1500);
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this reservation?')) {
      return;
    }

    setIsDeleting(true);

    // Simulate API call
    setTimeout(() => {
      alert('Reservation deleted successfully!');
      setIsDeleting(false);
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/tables`);
    }, 1500);
  };

  return (
    <>
      {/* Back Button */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`}>
          <button className="flex items-center gap-2 text-[#887663] hover:text-[#181511] transition-colors">
            <ArrowLeft size={20} />
            <span className="text-sm font-medium">Back to Tables</span>
          </button>
        </Link>
      </div>

      <div className="flex flex-col w-[512px] max-w-[512px] py-5 mx-auto">
        <div className="flex flex-wrap justify-between gap-3 p-4">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight min-w-72">Edit Reservation</p>
        </div>

        <form onSubmit={handleSubmit}>
          {/* Customer Name */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Customer Name</p>
              <input
                name="customerName"
                value={formData.customerName}
                onChange={handleInputChange}
                placeholder="Enter customer name"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Contact Number */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Contact Number</p>
              <input
                name="contactNumber"
                value={formData.contactNumber}
                onChange={handleInputChange}
                placeholder="Enter contact number"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Reservation Date */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Reservation Date</p>
              <div className="flex w-full flex-1 items-stretch rounded-xl">
                <input
                  name="reservationDate"
                  type="date"
                  value={formData.reservationDate}
                  onChange={handleInputChange}
                  placeholder="Select date"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                  required
                />
                <div className="text-[#887663] flex border-none bg-[#f4f2f0] items-center justify-center pr-4 rounded-r-xl border-l-0">
                  <Calendar size={24} />
                </div>
              </div>
            </label>
          </div>

          {/* Reservation Time */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Reservation Time</p>
              <div className="flex w-full flex-1 items-stretch rounded-xl">
                <input
                  name="reservationTime"
                  type="time"
                  value={formData.reservationTime}
                  onChange={handleInputChange}
                  placeholder="Select time"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 rounded-r-none border-r-0 pr-2 text-base font-normal leading-normal"
                  required
                />
                <div className="text-[#887663] flex border-none bg-[#f4f2f0] items-center justify-center pr-4 rounded-r-xl border-l-0">
                  <Clock size={24} />
                </div>
              </div>
            </label>
          </div>

          {/* Party Size */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Party Size</p>
              <input
                name="partySize"
                type="number"
                min="1"
                max="20"
                value={formData.partySize}
                onChange={handleInputChange}
                placeholder="Enter party size"
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal"
                required
              />
            </label>
          </div>

          {/* Table Preference */}
          <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
            <label className="flex flex-col min-w-40 flex-1">
              <p className="text-[#181511] text-base font-medium leading-normal pb-2">Table Preference</p>
              <select
                name="tablePreference"
                value={formData.tablePreference}
                onChange={handleInputChange}
                className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f0] focus:border-none h-14 placeholder:text-[#887663] p-4 text-base font-normal leading-normal appearance-none"
                style={{
                  backgroundImage: `url('data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 width=%2724px%27 height=%2724px%27 fill=%27rgb(136,118,99)%27 viewBox=%270 0 256 256%27%3e%3cpath d=%27M181.66,170.34a8,8,0,0,1,0,11.32l-48,48a8,8,0,0,1-11.32,0l-48-48a8,8,0,0,1,11.32-11.32L128,212.69l42.34-42.35A8,8,0,0,1,181.66,170.34Zm-96-84.68L128,43.31l42.34,42.35a8,8,0,0,0,11.32-11.32l-48-48a8,8,0,0,0-11.32,0l-48,48A8,8,0,0,0,85.66,85.66Z%27%3e%3c/path%3e%3c/svg%3e')`,
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'right 1rem center',
                  backgroundSize: '1.5rem'
                }}
              >
                <option value="">Select table preference</option>
                {mockTableOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="flex px-4 py-3 justify-between">
            <button
              type="button"
              onClick={handleDelete}
              disabled={isDeleting}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-red-500 text-white text-sm font-bold leading-normal tracking-[0.015em] hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed gap-2"
            >
              <Trash2 size={16} />
              <span className="truncate">{isDeleting ? 'Deleting...' : 'Delete'}</span>
            </button>

            <button
              type="submit"
              disabled={isSubmitting}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d4741a] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isSubmitting ? 'Updating...' : 'Update Reservation'}</span>
            </button>
          </div>
        </form>
      </div>
    </>
  );
}
