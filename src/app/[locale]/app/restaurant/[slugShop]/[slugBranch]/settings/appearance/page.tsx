'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft, Palette, Moon, Sun, Monitor, Check } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import AppearancePreview from '@/components/settings/AppearancePreview';
import { useAppearance } from '@/lib/context/AppearanceContext';

interface AppearanceSettingsPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

// Mock appearance settings
const mockAppearanceSettings = {
  theme: 'light',
  accentColor: 'earth',
  fontSize: 'medium',
  reducedMotion: false,
  reducedTransparency: false,
  highContrast: false,
  compactMode: false,
  customFont: 'be-vietnam',
  customCss: '',
};

// Color theme options
const colorThemes = [
  { id: 'earth', name: 'Earth Tones', primary: '#8a745c', secondary: '#e5ccb2', bg: '#fbfaf9', text: '#181510' },
  { id: 'ocean', name: 'Ocean Blue', primary: '#3b82f6', secondary: '#93c5fd', bg: '#f0f9ff', text: '#1e3a8a' },
  { id: 'forest', name: 'Forest Green', primary: '#059669', secondary: '#a7f3d0', bg: '#ecfdf5', text: '#064e3b' },
  { id: 'sunset', name: 'Sunset Orange', primary: '#ea580c', secondary: '#fdba74', bg: '#fff7ed', text: '#7c2d12' },
  { id: 'berry', name: 'Berry Purple', primary: '#8b5cf6', secondary: '#c4b5fd', bg: '#f5f3ff', text: '#4c1d95' },
];

// Font size options
const fontSizeOptions = [
  { id: 'small', name: 'Small', scale: 0.875 },
  { id: 'medium', name: 'Medium (Default)', scale: 1 },
  { id: 'large', name: 'Large', scale: 1.125 },
  { id: 'x-large', name: 'Extra Large', scale: 1.25 },
];

// Font family options
const fontFamilyOptions = [
  { id: 'be-vietnam', name: 'Be Vietnam Pro (Default)', fontFamily: 'Be Vietnam Pro, sans-serif' },
  { id: 'inter', name: 'Inter', fontFamily: 'Inter, sans-serif' },
  { id: 'roboto', name: 'Roboto', fontFamily: 'Roboto, sans-serif' },
  { id: 'poppins', name: 'Poppins', fontFamily: 'Poppins, sans-serif' },
  { id: 'montserrat', name: 'Montserrat', fontFamily: 'Montserrat, sans-serif' },
];

export default function AppearanceSettingsPage({ params }: AppearanceSettingsPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('theme');
  const [isSaving, setIsSaving] = useState(false);

  // Use the global appearance context
  const { settings, updateSettings } = useAppearance();

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  const handleSave = () => {
    setIsSaving(true);

    // Simulate API call - in real app, this would save to backend
    setTimeout(() => {
      setIsSaving(false);
      toast.success('Appearance settings saved successfully! Changes applied to the website.');
    }, 1000);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    // Update settings immediately - this will trigger the appearance changes
    updateSettings({ [field]: value });
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Appearance Settings</h1>
          <p className="text-[#8a745c] text-sm">Customize the look and feel of your restaurant dashboard for {shop.name} - {branch.name}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-[#f1edea] mb-6">
              <TabsTrigger value="theme" className="data-[state=active]:bg-[#fbfaf9]">
                <Palette className="h-4 w-4 mr-2" />
                Theme & Colors
              </TabsTrigger>
              <TabsTrigger value="typography" className="data-[state=active]:bg-[#fbfaf9]">
                <span className="font-serif mr-2">A</span>
                Typography
              </TabsTrigger>
              <TabsTrigger value="accessibility" className="data-[state=active]:bg-[#fbfaf9]">
                <span className="mr-2">👁️</span>
                Accessibility
              </TabsTrigger>
            </TabsList>

            <TabsContent value="theme" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Theme Mode</CardTitle>
                  <CardDescription>Choose between light, dark, or system theme</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={settings.theme}
                    onValueChange={(value) => handleInputChange('theme', value)}
                    className="flex flex-col space-y-1 sm:flex-row sm:space-x-4 sm:space-y-0"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="light" id="theme-light" />
                      <Label htmlFor="theme-light" className="flex items-center">
                        <Sun className="h-4 w-4 mr-2" />
                        Light
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="dark" id="theme-dark" />
                      <Label htmlFor="theme-dark" className="flex items-center">
                        <Moon className="h-4 w-4 mr-2" />
                        Dark
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="system" id="theme-system" />
                      <Label htmlFor="theme-system" className="flex items-center">
                        <Monitor className="h-4 w-4 mr-2" />
                        System
                      </Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>

              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Color Theme</CardTitle>
                  <CardDescription>Choose a color theme for your dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {colorThemes.map((theme) => (
                      <div
                        key={theme.id}
                        className={cn(
                          "flex flex-col p-4 rounded-lg border-2 cursor-pointer transition-all",
                          settings.accentColor === theme.id
                            ? "border-[#8a745c]"
                            : "border-[#e5e1dc] hover:border-[#d6bd9e]"
                        )}
                        onClick={() => handleInputChange('accentColor', theme.id)}
                      >
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium text-[#181510]">{theme.name}</span>
                          {settings.accentColor === theme.id && (
                            <Check className="h-4 w-4 text-[#8a745c]" />
                          )}
                        </div>
                        <div className="flex gap-2">
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.primary }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.secondary }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full border border-[#e5e1dc]"
                            style={{ backgroundColor: theme.bg }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.text }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="typography" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Font Size</CardTitle>
                  <CardDescription>Adjust the size of text throughout the dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={settings.fontSize}
                    onValueChange={(value) => handleInputChange('fontSize', value)}
                    className="space-y-4"
                  >
                    {fontSizeOptions.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.id} id={`font-size-${option.id}`} />
                        <Label
                          htmlFor={`font-size-${option.id}`}
                          style={{ fontSize: `${option.scale}rem` }}
                        >
                          {option.name}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>

              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Font Family</CardTitle>
                  <CardDescription>Choose a font family for the dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={settings.customFont}
                    onValueChange={(value) => handleInputChange('customFont', value)}
                    className="space-y-4"
                  >
                    {fontFamilyOptions.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.id} id={`font-family-${option.id}`} />
                        <Label
                          htmlFor={`font-family-${option.id}`}
                          style={{ fontFamily: option.fontFamily }}
                          className="text-lg"
                        >
                          {option.name}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="accessibility" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Accessibility Options</CardTitle>
                  <CardDescription>Adjust settings to improve accessibility</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="reduced-motion" className="text-base">Reduced Motion</Label>
                      <p className="text-sm text-[#8a745c]">Minimize animations and transitions</p>
                    </div>
                    <Switch
                      id="reduced-motion"
                      checked={settings.reducedMotion}
                      onCheckedChange={(checked) => handleInputChange('reducedMotion', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="reduced-transparency" className="text-base">Reduced Transparency</Label>
                      <p className="text-sm text-[#8a745c]">Reduce transparency and blur effects</p>
                    </div>
                    <Switch
                      id="reduced-transparency"
                      checked={settings.reducedTransparency}
                      onCheckedChange={(checked) => handleInputChange('reducedTransparency', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="high-contrast" className="text-base">High Contrast</Label>
                      <p className="text-sm text-[#8a745c]">Increase contrast for better readability</p>
                    </div>
                    <Switch
                      id="high-contrast"
                      checked={settings.highContrast}
                      onCheckedChange={(checked) => handleInputChange('highContrast', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="compact-mode" className="text-base">Compact Mode</Label>
                      <p className="text-sm text-[#8a745c]">Reduce spacing between elements</p>
                    </div>
                    <Switch
                      id="compact-mode"
                      checked={settings.compactMode}
                      onCheckedChange={(checked) => handleInputChange('compactMode', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="lg:sticky lg:top-20">
          <AppearancePreview
            theme={settings.theme}
            accentColor={settings.accentColor}
            fontSize={settings.fontSize}
            customFont={settings.customFont}
            reducedMotion={settings.reducedMotion}
            highContrast={settings.highContrast}
            compactMode={settings.compactMode}
          />
        </div>
      </div>

      <div className="mt-6 flex justify-end">
        <Button
          className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
          onClick={handleSave}
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}
