'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Camera, Mail, Phone, User, MapPin, Shield, Key } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';

interface ProfileSettingsPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

// Mock user data
const mockUserData = {
  id: 'user-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+****************',
  role: 'Restaurant Owner',
  avatar: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBTI0Z2xPUTyhRVeL1K9vLjQGbi-1UHxVlqwNVDF-iTr92vrWHqqemi73fpc8wOTxMsEWHp2sMWTYeuRGEAvkoprEmdcqQKjv1ysjZPz-Oxjj0nwmNth_9fIg4-n1G3JRRNt6U7R2hsx-S0tKSQkNScC_IoX_R82wZ86pqUnzHoPxbvYmN3_czqGASpoFbwwL2zK_CrjOxPpt8-qcbimmno5Mh8rjWKzJy07NeVJl7_QAhgvUVft3cXpPUn8wHTLxZSJXRyU2pkKbK3',
  bio: 'Passionate restaurant owner with over 10 years of experience in the food industry.',
  address: '123 Main St, Anytown, CA 12345',
  language: 'en',
  timezone: 'America/Los_Angeles',
  twoFactorEnabled: false,
  emailNotifications: true,
  pushNotifications: true,
  lastLogin: '2023-06-15T10:30:00Z',
  createdAt: '2020-03-10T08:15:00Z',
};

export default function ProfileSettingsPage({ params }: ProfileSettingsPageProps) {
  const { slugShop, slugBranch } = params;
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('personal');
  const [userData, setUserData] = useState(mockUserData);
  const [isSaving, setIsSaving] = useState(false);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  const handleSave = () => {
    setIsSaving(true);

    // Simulate API call
    setTimeout(() => {
      setIsSaving(false);
      toast.success('Profile settings saved successfully');
    }, 1000);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setUserData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAvatarChange = () => {
    // In a real app, this would open a file picker
    toast.info('Avatar upload functionality would be implemented here');
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Profile Settings</h1>
          <p className="text-[#8a745c] text-sm">Manage your personal information and account settings</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardContent className="p-6">
              <div className="flex flex-col items-center">
                <div className="relative mb-4">
                  <Avatar className="w-32 h-32 border-4 border-[#e5e1dc]">
                    <AvatarImage src={userData.avatar} alt={userData.name} />
                    <AvatarFallback className="bg-[#e5ccb2] text-[#181510] text-2xl">
                      {userData.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <button
                    className="absolute bottom-0 right-0 bg-[#e5ccb2] text-[#181510] rounded-full p-2 shadow-md hover:bg-[#d6bd9e] transition-colors"
                    onClick={handleAvatarChange}
                  >
                    <Camera className="h-4 w-4" />
                  </button>
                </div>
                <h2 className="text-[#181510] text-xl font-bold mb-1">{userData.name}</h2>
                <p className="text-[#8a745c] text-sm mb-4">{userData.role}</p>
                <div className="w-full space-y-2 text-sm">
                  <div className="flex items-center gap-2 text-[#8a745c]">
                    <Mail className="h-4 w-4" />
                    <span>{userData.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-[#8a745c]">
                    <Phone className="h-4 w-4" />
                    <span>{userData.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-[#8a745c]">
                    <MapPin className="h-4 w-4" />
                    <span>{userData.address}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-[#f1edea] mb-6">
              <TabsTrigger value="personal" className="data-[state=active]:bg-[#fbfaf9]">
                <User className="h-4 w-4 mr-2" />
                Personal Info
              </TabsTrigger>
              <TabsTrigger value="security" className="data-[state=active]:bg-[#fbfaf9]">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
              <TabsTrigger value="preferences" className="data-[state=active]:bg-[#fbfaf9]">
                <Key className="h-4 w-4 mr-2" />
                Preferences
              </TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>Update your personal details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={userData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="bg-white border-[#e5e1dc]"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={userData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="bg-white border-[#e5e1dc]"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={userData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        className="bg-white border-[#e5e1dc]"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Input
                        id="role"
                        value={userData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                        className="bg-white border-[#e5e1dc]"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={userData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      className="bg-white border-[#e5e1dc]"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      value={userData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      className="bg-white border-[#e5e1dc] min-h-[100px]"
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
                    onClick={handleSave}
                    disabled={isSaving}
                  >
                    {isSaving ? 'Saving...' : 'Save Changes'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>Manage your account security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#181510] font-medium">Change Password</h3>
                        <p className="text-[#8a745c] text-sm">Update your account password</p>
                      </div>
                      <Button variant="outline" className="border-[#e5e1dc]">
                        Change Password
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#181510] font-medium">Two-Factor Authentication</h3>
                        <p className="text-[#8a745c] text-sm">Add an extra layer of security to your account</p>
                      </div>
                      <Switch
                        checked={userData.twoFactorEnabled}
                        onCheckedChange={(checked) => handleInputChange('twoFactorEnabled', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#181510] font-medium">Login History</h3>
                        <p className="text-[#8a745c] text-sm">View your recent login activity</p>
                      </div>
                      <Button variant="outline" className="border-[#e5e1dc]">
                        View History
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preferences" className="space-y-6">
              <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
                <CardHeader>
                  <CardTitle>User Preferences</CardTitle>
                  <CardDescription>Customize your account experience</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">Language</Label>
                      <select
                        id="language"
                        value={userData.language}
                        onChange={(e) => handleInputChange('language', e.target.value)}
                        className="w-full rounded-md border border-[#e5e1dc] bg-white px-3 py-2 text-sm"
                      >
                        <option value="en">English</option>
                        <option value="th">Thai</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <select
                        id="timezone"
                        value={userData.timezone}
                        onChange={(e) => handleInputChange('timezone', e.target.value)}
                        className="w-full rounded-md border border-[#e5e1dc] bg-white px-3 py-2 text-sm"
                      >
                        <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="Asia/Bangkok">Bangkok</option>
                        <option value="Europe/London">London</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#181510] font-medium">Email Notifications</h3>
                        <p className="text-[#8a745c] text-sm">Receive email updates about your account</p>
                      </div>
                      <Switch
                        checked={userData.emailNotifications}
                        onCheckedChange={(checked) => handleInputChange('emailNotifications', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#181510] font-medium">Push Notifications</h3>
                        <p className="text-[#8a745c] text-sm">Receive push notifications on your devices</p>
                      </div>
                      <Switch
                        checked={userData.pushNotifications}
                        onCheckedChange={(checked) => handleInputChange('pushNotifications', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
                    onClick={handleSave}
                    disabled={isSaving}
                  >
                    {isSaving ? 'Saving...' : 'Save Preferences'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
