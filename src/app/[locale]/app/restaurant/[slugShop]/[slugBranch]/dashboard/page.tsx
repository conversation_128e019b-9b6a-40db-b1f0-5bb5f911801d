'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Users, Utensils, Calendar, Star, BarChart3, DollarSign, Settings } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import React from 'react';

interface DashboardPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Restaurant
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">
            {shop.name} - {branch.name}
          </h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Dashboard for {branch.name} branch
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Today's Orders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-[#e58219] mr-2" />
              <div className="text-2xl font-bold text-[#181510]">24</div>
            </div>
            <p className="text-xs text-[#8a745c] mt-1">+12% from yesterday</p>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-[#e58219] mr-2" />
              <div className="text-2xl font-bold text-[#181510]">$1,240</div>
            </div>
            <p className="text-xs text-[#8a745c] mt-1">+8% from last week</p>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Reservations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-[#e58219] mr-2" />
              <div className="text-2xl font-bold text-[#181510]">12</div>
            </div>
            <p className="text-xs text-[#8a745c] mt-1">For today</p>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-[#8a745c]">Rating</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Star className="h-8 w-8 text-[#e58219] mr-2" />
              <div className="text-2xl font-bold text-[#181510]">4.8</div>
            </div>
            <p className="text-xs text-[#8a745c] mt-1">Based on 120 reviews</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Utensils className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Tables</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage tables, layout, and QR codes</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Utensils className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Menu</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage menu items, categories, and pricing</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <DollarSign className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Orders</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage orders, payments, and deliveries</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Calendar className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Reservations</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage bookings, waitlist, and availability</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Users className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Staff</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage employees, roles, and schedules</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reviews`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Star className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Reviews</CardTitle>
              <p className="text-[#8a745c] text-sm">Manage customer feedback and ratings</p>
            </CardContent>
          </Card>
        </Link>

        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`} className="block">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-md transition-shadow h-full">
            <CardContent className="p-6 flex flex-col items-center text-center">
              <div className="w-16 h-16 rounded-full bg-[#f1edea] flex items-center justify-center mb-4">
                <Settings className="h-8 w-8 text-[#8a745c]" />
              </div>
              <CardTitle className="text-[#181510] mb-2">Settings</CardTitle>
              <p className="text-[#8a745c] text-sm">Configure branch settings and preferences</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
