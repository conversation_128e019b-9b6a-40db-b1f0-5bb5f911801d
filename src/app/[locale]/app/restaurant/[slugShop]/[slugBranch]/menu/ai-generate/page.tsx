'use client';

import { useTranslations } from 'next-intl';
import { useState, useRef, ChangeEvent } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// Mock data for generated menu items
const mockGeneratedMenuItems = [
  {
    id: '1',
    name: 'Truffle Mushroom Risotto',
    description: 'Creamy Arborio rice cooked with wild mushrooms, finished with truffle oil and Parmesan cheese',
    price: 18.99,
    category: 'Main Course',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBitRJJIAwrsT3sN69YFrCfepOrR3pneJ4inWpwR7iu2tNMseZE90YE_oCNgkmq5Gs_UyUyocZX_daSu7NymBhRzfJNJRLDw3DRHhJ3HgXSSPZMyBTglltX4eAZaQ3guLhNiLPkYbwwmj77cLZogMsBiBm-6X4wKpxb-20L1PlcjmWmV_OTwZFlsVy_Q-52Bw-nvA7xF1H6egrCjRuyBkQz_H9jh_Ooe-9XgXYdUb8yvdgkJPgPXxmjpmHDFdx4aHichBxyNhdEo8WW',
  },
  {
    id: '2',
    name: 'Miso Glazed Black Cod',
    description: 'Black cod marinated in sweet miso, served with baby bok choy and jasmine rice',
    price: 24.99,
    category: 'Main Course',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAh8eYEyYXcyDpyWFKDDznvVgHbZvZ4yZ2oYryS_xXIjWO4btMh2mDn-5wf0PfxySVEtgu4fDWbtVP7Dp3NLEH0Q6eGsxYDhzxM2JSjRERtf_KWk8l__iWRPfEdVXGcLiMbV6VZX9BUQAPwi0IsyFMZxyxV0NBKpRSeVusBrtb8SAlPvpOAdw8mlFF7jfZLgj2DT2QQ0YQQ1EZeB1XBtXk0TUYnxEzUxzEhSCz6YQyD8xRa8e51R2p_Xw7zSTIkOPfivki3ZcqSe_Aw',
  },
  {
    id: '3',
    name: 'Roasted Beet & Goat Cheese Salad',
    description: 'Roasted beets with arugula, candied walnuts, goat cheese, and balsamic reduction',
    price: 12.99,
    category: 'Appetizer',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBc_5w7kE0XNZCk4Pq4jqkUxZdRoKkepiOpjfNL5BIi01dp6yZjD50AVlFE2lqFwrRtOF8PAFT9drxY9JdynacoLa6ZEON6w1B75S4Kg-pnruigJm0TnHxMFTcNm1zkuerGKYM-yhLgnFrDKMAGnD2MaH2Kb6_-ljQhXJiAl8wyOfSRrlNSs8zV5Z-XgYJflJD5EiQuiJbDMfkGyI4ZY4zs3BUSZ-MyVgWgTvG2YcsZSsXcxSpY8qUpAut93DiaQoMQnmfKhWyPLAQS',
  },
  {
    id: '4',
    name: 'Lavender Crème Brûlée',
    description: 'Classic vanilla custard infused with lavender, topped with caramelized sugar',
    price: 9.99,
    category: 'Dessert',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAVXkcgopl7C3R5rhVVK87k708mcOJDX1X3B9GJGfJQibNh4QJOFwf3L_gyHP-EJFOpauOSpWhnjVZjkCQZH4l2ZLG7cm1AMWeP735xdYsvrqoEjaVum4t_WPAGx7DC2ePvvdrfLSn21MhdzPa02Iw-HrlJbSRHyddMWBvgPxnLwf_xh9vbbBB2xWAfk50ikquhZ0o7BmgxSjw-pi8pn2Q0uEkwqVulcXsCYVdTnhKmeun-XuuQBOIiglpoDE9DIwhYXgHcjSG2r_kq',
  },
  {
    id: '5',
    name: 'Spicy Tuna Tartare',
    description: 'Fresh tuna mixed with avocado, cucumber, and spicy mayo, served with wonton crisps',
    price: 16.99,
    category: 'Appetizer',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBxlonv4RXd0k1X6nNlnWz7HkK7tda0fTHRZKhsEdu4AhkJns9RpHwe5FzAzrGhEvv2gb69h6Pq6u-KcZTO0eItX-tlQatn-ulW0_Vf_QqXLCcC2nlE_Z7AWrpHtjG5EUnSlY7_9rFRcgdfKfRFhYxE_aHcsHUaISRqE9Axj0a1kqkb-NqZk8d-O8j8XCxh_1uPHf2RXJxJYtcZaoM2CBW3TAbMM-HHZJwlm_nlU6GiXw30wPVmjriOvfDiz0O_A0QZBSJm8jVjpYv',
  },
];

export default function AIGenerateMenuPage() {
  const t = useTranslations('restaurant');
  const router = useRouter();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'menu-image' | 'text' | 'food-images'>('menu-image');

  // State for file uploads
  const [menuImage, setMenuImage] = useState<File | null>(null);
  const [menuImagePreview, setMenuImagePreview] = useState<string | null>(null);
  const [foodImages, setFoodImages] = useState<File[]>([]);
  const [foodImagesPreview, setFoodImagesPreview] = useState<string[]>([]);

  // State for text input
  const [menuText, setMenuText] = useState('');

  // State for generated menu items
  const [generatedItems, setGeneratedItems] = useState<typeof mockGeneratedMenuItems>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Refs for file inputs
  const menuImageInputRef = useRef<HTMLInputElement>(null);
  const foodImagesInputRef = useRef<HTMLInputElement>(null);

  // Handle menu image upload
  const handleMenuImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setMenuImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = () => {
        setMenuImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle food images upload
  const handleFoodImagesUpload = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      setFoodImages(prevImages => [...prevImages, ...files]);

      // Create previews
      files.forEach(file => {
        const reader = new FileReader();
        reader.onload = () => {
          setFoodImagesPreview(prev => [...prev, reader.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  // Handle text input
  const handleTextChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setMenuText(e.target.value);
  };

  // Handle form submission for menu image
  const handleGenerateFromMenuImage = () => {
    if (!menuImage) return;

    setIsGenerating(true);

    // Simulate API call delay
    setTimeout(() => {
      // Instead of showing preview, navigate to review page
      router.push('/app/restaurant/menu/ai-generate/review');
    }, 2000);
  };

  // Handle form submission for text
  const handleGenerateFromText = () => {
    if (!menuText.trim()) return;

    setIsGenerating(true);

    // Simulate API call delay
    setTimeout(() => {
      // Instead of showing preview, navigate to review page
      router.push('/app/restaurant/menu/ai-generate/review');
    }, 2000);
  };

  // Handle form submission for food images
  const handleGenerateFromFoodImages = () => {
    if (foodImages.length === 0) return;

    setIsGenerating(true);

    // Simulate API call delay
    setTimeout(() => {
      // Instead of showing preview, navigate to review page
      router.push('/app/restaurant/menu/ai-generate/review');
    }, 2000);
  };

  // Handle adding items to menu
  const handleAddToMenu = () => {
    // In a real implementation, this would call an API to add the items to the menu
    alert('Items added to menu successfully!');
    router.push('/app/restaurant/menu');
  };

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">AI Menu Generator</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">Generate menu items using AI based on your restaurant's menu images, text, or food photos.</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="pb-3">
        <div className="flex border-b border-[#e5e1dc] px-4 gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'menu-image' ? 'border-b-[#e5ccb2] text-[#181511]' : 'border-b-transparent text-[#887663]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('menu-image'); }}
          >
            <p className={`${activeTab === 'menu-image' ? 'text-[#181511]' : 'text-[#887663]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Menu Image</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'text' ? 'border-b-[#e5ccb2] text-[#181511]' : 'border-b-transparent text-[#887663]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('text'); }}
          >
            <p className={`${activeTab === 'text' ? 'text-[#181511]' : 'text-[#887663]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Text</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'food-images' ? 'border-b-[#e5ccb2] text-[#181511]' : 'border-b-transparent text-[#887663]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('food-images'); }}
          >
            <p className={`${activeTab === 'food-images' ? 'text-[#181511]' : 'text-[#887663]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Food Images</p>
          </a>
        </div>
      </div>

      {/* Menu Image Tab */}
      {activeTab === 'menu-image' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-[#887663] text-sm font-normal leading-normal mb-4">
              Upload a clear image of your menu, and our AI will automatically create an interactive online menu for your restaurant.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e5e1dc] px-6 py-14">
              {menuImagePreview ? (
                <div className="flex flex-col items-center gap-4 w-full">
                  <img
                    src={menuImagePreview}
                    alt="Menu preview"
                    className="max-w-full max-h-[300px] object-contain rounded-lg"
                  />
                  <button
                    onClick={() => {
                      setMenuImage(null);
                      setMenuImagePreview(null);
                    }}
                    className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
                  >
                    <span className="truncate">Remove</span>
                  </button>
                </div>
              ) : (
                <>
                  <div className="flex max-w-[480px] flex-col items-center gap-2">
                    <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Drag and drop or browse to upload</p>
                    <p className="text-[#887663] text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG. Max size: 5MB</p>
                  </div>
                  <input
                    type="file"
                    ref={menuImageInputRef}
                    onChange={handleMenuImageUpload}
                    accept="image/jpeg,image/png"
                    className="hidden"
                  />
                  <button
                    onClick={() => menuImageInputRef.current?.click()}
                    className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
                  >
                    <span className="truncate">Browse Files</span>
                  </button>
                </>
              )}
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <button
              onClick={handleGenerateFromMenuImage}
              disabled={!menuImage || isGenerating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </button>
          </div>
        </>
      )}

      {/* Text Tab */}
      {activeTab === 'text' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-[#887663] text-sm font-normal leading-normal mb-4">
              Enter your menu items as text, and our AI will format them into a structured menu with descriptions and prices.
            </p>

            <div className="flex flex-col gap-4">
              <textarea
                value={menuText}
                onChange={handleTextChange}
                placeholder="Enter your menu items here... For example:&#10;&#10;APPETIZERS&#10;Garlic Bread - $5.99&#10;Mozzarella Sticks - $7.99&#10;&#10;MAIN COURSES&#10;Spaghetti Bolognese - $14.99&#10;Grilled Salmon - $18.99"
                className="w-full min-h-[300px] p-4 border border-[#e5e1dc] rounded-xl resize-none focus:outline-none focus:ring-1 focus:ring-[#e5ccb2] text-[#181511]"
              />
            </div>
          </div>

          <div className="flex px-4 py-3 justify-end">
            <button
              onClick={handleGenerateFromText}
              disabled={!menuText.trim() || isGenerating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </button>
          </div>
        </>
      )}

      {/* Food Images Tab */}
      {activeTab === 'food-images' && (
        <>
          <div className="flex flex-col p-4">
            <p className="text-[#887663] text-sm font-normal leading-normal mb-4">
              Upload photos of your dishes, and our AI will create menu items with descriptions and suggested prices.
            </p>

            <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e5e1dc] px-6 py-14">
              <div className="flex max-w-[480px] flex-col items-center gap-2">
                <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Drag and drop or browse to upload</p>
                <p className="text-[#887663] text-sm font-normal leading-normal max-w-[480px] text-center">Supported formats: JPG, PNG. Max size: 5MB per image</p>
              </div>
              <input
                type="file"
                ref={foodImagesInputRef}
                onChange={handleFoodImagesUpload}
                accept="image/jpeg,image/png"
                multiple
                className="hidden"
              />
              <button
                onClick={() => foodImagesInputRef.current?.click()}
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span className="truncate">Browse Files</span>
              </button>
            </div>

            {foodImagesPreview.length > 0 && (
              <div className="mt-6">
                <p className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] mb-4">Uploaded Images</p>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {foodImagesPreview.map((preview, index) => (
                    <div key={index} className="relative">
                      <img
                        src={preview}
                        alt={`Food image ${index + 1}`}
                        className="w-full h-40 object-cover rounded-lg"
                      />
                      <button
                        onClick={() => {
                          setFoodImages(prev => prev.filter((_, i) => i !== index));
                          setFoodImagesPreview(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#181511" viewBox="0 0 256 256">
                          <path d="M205.66,194.34a8,8,0,0,1-11.32,11.32L128,139.31,61.66,205.66a8,8,0,0,1-11.32-11.32L116.69,128,50.34,61.66A8,8,0,0,1,61.66,50.34L128,116.69l66.34-66.35a8,8,0,0,1,11.32,11.32L139.31,128Z"></path>
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="flex px-4 py-3 justify-end">
            <button
              onClick={handleGenerateFromFoodImages}
              disabled={foodImages.length === 0 || isGenerating}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{isGenerating ? 'Generating...' : 'Generate Menu'}</span>
            </button>
          </div>
        </>
      )}

      {/* Generated Menu Items Preview */}
      {showPreview && (
        <>
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Generated Menu Items</h3>

          <div className="px-4 py-3 @container">
            <div className="flex overflow-hidden rounded-lg border border-[#e5e1dc] bg-white">
              <table className="flex-1">
                <thead>
                  <tr className="bg-white">
                    <th className="table-column-120 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Item</th>
                    <th className="table-column-240 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Category</th>
                    <th className="table-column-360 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Description</th>
                    <th className="table-column-480 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Price</th>
                  </tr>
                </thead>
                <tbody>
                  {generatedItems.map((item) => (
                    <tr key={item.id} className="border-t border-t-[#e5e1dc]">
                      <td className="table-column-120 h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                        <div className="flex items-center gap-3">
                          <div
                            className="bg-center bg-no-repeat aspect-square bg-cover rounded-full w-10"
                            style={{backgroundImage: `url("${item.image}")`}}
                          ></div>
                          <span>{item.name}</span>
                        </div>
                      </td>
                      <td className="table-column-240 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        {item.category}
                      </td>
                      <td className="table-column-360 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        {item.description}
                      </td>
                      <td className="table-column-480 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                        ${item.price.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <style dangerouslySetInnerHTML={{
              __html: `
                @container(max-width:120px){.table-column-120{display: none;}}
                @container(max-width:240px){.table-column-240{display: none;}}
                @container(max-width:360px){.table-column-360{display: none;}}
                @container(max-width:480px){.table-column-480{display: none;}}
              `
            }} />
          </div>

          <div className="flex px-4 py-3 gap-3">
            <button
              onClick={handleAddToMenu}
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
            >
              <span className="truncate">Add to Menu</span>
            </button>

            <Link href="/app/restaurant/menu">
              <button
                className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              >
                <span className="truncate">Cancel</span>
              </button>
            </Link>
          </div>
        </>
      )}
    </>
  );
}
