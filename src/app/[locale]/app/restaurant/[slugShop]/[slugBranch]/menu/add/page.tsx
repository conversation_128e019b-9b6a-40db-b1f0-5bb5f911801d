'use client';

import React, { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { useCreateMenuItemMutation } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@/i18n/navigation';

// Form schema
const addMenuItemSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  price: z.preprocess(
    (val) => (val === '' ? 0 : Number(val)),
    z.number().min(0, 'Price must be a positive number')
  ),
  category: z.string().min(1, 'Category is required'),
  ingredients: z.string().optional(),
  nutritionalInfo: z.string().optional(),
});

type AddMenuItemFormValues = z.infer<typeof addMenuItemSchema>;

interface AddMenuItemPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function AddMenuItemPage({ params }: AddMenuItemPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const t = useTranslations('restaurant');
  const router = useRouter();
  const [createMenuItem, { isLoading }] = useCreateMenuItemMutation();

  // State for image upload
  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AddMenuItemFormValues>({
    resolver: zodResolver(addMenuItemSchema),
    defaultValues: {
      name: '',
      description: '',
      price: 0,
      category: '',
      ingredients: '',
      nutritionalInfo: '',
    },
  });

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const onSubmit = async (data: AddMenuItemFormValues) => {
    try {
      // In a real app, you would upload the image to a storage service
      // and get back a URL to store in the database
      const imageUrl = imagePreview || '';

      // Process ingredients and nutritional info
      const ingredientsArray = data.ingredients
        ? data.ingredients.split('\n').filter(item => item.trim() !== '')
        : [];

      // Process nutritional info
      const nutritionalInfoObj: Record<string, any> = {};
      if (data.nutritionalInfo) {
        const lines = data.nutritionalInfo.split('\n');
        for (const line of lines) {
          if (line.includes(':')) {
            const [key, value] = line.split(':').map(item => item.trim());
            if (key && value) {
              nutritionalInfoObj[key] = value;
            }
          }
        }
      }

      // For demo purposes, we're using a mock merchant ID
      // In a real app, you would get this from the authenticated user's context
      const merchantId = 'demo-merchant-id';

      await createMenuItem({
        merchantId,
        name: data.name,
        description: data.description || '',
        price: data.price,
        category: data.category,
        image: imageUrl,
        available: true,
        ingredients: ingredientsArray,
        allergens: [],
        nutritionalInfo: nutritionalInfoObj,
        preparationTime: 0,
      }).unwrap();

      toast.success('Menu item added successfully');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu`);
    } catch (error) {
      console.error('Failed to add menu item:', error);
      toast.error('Failed to add menu item');
    }
  };

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6 p-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Menu
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 p-4">
        <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight min-w-72">Add New Menu Item</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col max-w-[512px] mx-auto">
        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Item Name</p>
            <input
              {...register('name')}
              placeholder="Enter item name"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-14 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Description</p>
            <textarea
              {...register('description')}
              placeholder="Enter item description"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none min-h-36 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Price</p>
            <input
              {...register('price')}
              placeholder="Enter item price"
              type="number"
              step="0.01"
              min="0"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-14 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.price && <p className="text-red-500 text-sm mt-1">{errors.price.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Category</p>
            <select
              {...register('category')}
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none h-14 bg-[image:--select-button-svg] placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            >
              <option value="">Select category</option>
              <option value="appetizer">Appetizer</option>
              <option value="mainCourse">Main Course</option>
              <option value="dessert">Dessert</option>
              <option value="beverage">Beverage</option>
              <option value="side">Side</option>
            </select>
            {errors.category && <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Ingredients</p>
            <textarea
              {...register('ingredients')}
              placeholder="List ingredients"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none min-h-36 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.ingredients && <p className="text-red-500 text-sm mt-1">{errors.ingredients.message}</p>}
          </label>
        </div>

        <div className="flex max-w-[480px] flex-wrap items-end gap-4 px-4 py-3">
          <label className="flex flex-col min-w-40 flex-1">
            <p className="text-[#161412] text-base font-medium leading-normal pb-2">Nutritional Information</p>
            <textarea
              {...register('nutritionalInfo')}
              placeholder="Enter nutritional information"
              className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#161412] focus:outline-0 focus:ring-0 border-none bg-[#f4f2f1] focus:border-none min-h-36 placeholder:text-[#81766a] p-4 text-base font-normal leading-normal"
            />
            {errors.nutritionalInfo && <p className="text-red-500 text-sm mt-1">{errors.nutritionalInfo.message}</p>}
          </label>
        </div>

        <div className="flex flex-col p-4">
          <div className="flex flex-col items-center gap-6 rounded-xl border-2 border-dashed border-[#e3e1dd] px-6 py-14">
            {imagePreview ? (
              <div className="flex flex-col items-center gap-4">
                <img src={imagePreview} alt="Preview" className="max-w-[200px] max-h-[200px] rounded-lg" />
                <button
                  type="button"
                  onClick={() => {
                    setImage(null);
                    setImagePreview(null);
                  }}
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span className="truncate">Remove Image</span>
                </button>
              </div>
            ) : (
              <>
                <div className="flex max-w-[480px] flex-col items-center gap-2">
                  <p className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] max-w-[480px] text-center">Upload Image</p>
                  <p className="text-[#161412] text-sm font-normal leading-normal max-w-[480px] text-center">Click or drag an image here to upload</p>
                </div>
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
                <label
                  htmlFor="image-upload"
                  className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f1] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em]"
                >
                  <span className="truncate">Upload Image</span>
                </label>
              </>
            )}
          </div>
        </div>

        <div className="flex px-4 py-3 justify-end">
          <button
            type="submit"
            disabled={isLoading}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e5ccb2] text-[#161412] text-sm font-bold leading-normal tracking-[0.015em] disabled:opacity-50"
          >
            <span className="truncate">{isLoading ? 'Adding...' : 'Add Item'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
