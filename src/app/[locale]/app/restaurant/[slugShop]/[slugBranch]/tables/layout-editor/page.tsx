'use client';

import { useState, useEffect, useRef } from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { useGetTablesQuery, useCreateTableMutation, useUpdateTableMutation, useDeleteTableMutation } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  Move,
  Square,
  Circle,
  Settings,
  Edit,
  LayoutGrid
} from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';

interface LayoutEditorPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

// Mock data for tables (will be replaced with real data from API)
const mockTables = [
  {
    id: '1',
    number: 1,
    capacity: 4,
    status: 'available',
    location: 'dining',
    shape: 'square',
    positionX: 100,
    positionY: 100,
    width: 80,
    height: 80,
  },
  {
    id: '2',
    number: 2,
    capacity: 2,
    status: 'available',
    location: 'dining',
    shape: 'circle',
    positionX: 250,
    positionY: 100,
    width: 60,
    height: 60,
  },
  {
    id: '3',
    number: 3,
    capacity: 6,
    status: 'available',
    location: 'dining',
    shape: 'rectangle',
    positionX: 100,
    positionY: 250,
    width: 120,
    height: 80,
  },
  {
    id: '4',
    number: 4,
    capacity: 2,
    status: 'available',
    location: 'outdoor',
    shape: 'circle',
    positionX: 400,
    positionY: 150,
    width: 60,
    height: 60,
  },
  {
    id: '5',
    number: 5,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
    shape: 'square',
    positionX: 400,
    positionY: 300,
    width: 80,
    height: 80,
  },
];

// Define default floors
const defaultFloors = [
  { id: 'floor-1', name: 'Ground Floor', order: 1 },
  { id: 'floor-2', name: 'First Floor', order: 2 },
];

// Define default areas
const defaultAreas = [
  { id: 'dining', name: 'Dining Area', floorId: 'floor-1' },
  { id: 'outdoor', name: 'Outdoor Patio', floorId: 'floor-1' },
  { id: 'bar', name: 'Bar Area', floorId: 'floor-1' },
  { id: 'private', name: 'Private Room', floorId: 'floor-1' },
  { id: 'vip', name: 'VIP Lounge', floorId: 'floor-2' },
  { id: 'event', name: 'Event Space', floorId: 'floor-2' },
];

// Define table shapes
const tableShapes = [
  { id: 'square', name: 'Square', icon: <Square className="h-4 w-4" /> },
  { id: 'circle', name: 'Circle', icon: <Circle className="h-4 w-4" /> },
  { id: 'rectangle', name: 'Rectangle', icon: <Square className="h-4 w-4 rotate-90" /> },
];

interface Floor {
  id: string;
  name: string;
  order: number;
}

interface Area {
  id: string;
  name: string;
  floorId: string;
}

interface Table {
  id: string;
  number: number;
  capacity: number;
  status: string;
  location: string; // area id
  shape: string;
  positionX: number;
  positionY: number;
  width: number;
  height: number;
}

export default function LayoutEditorPage({ params }: LayoutEditorPageProps) {
  const { slugShop, slugBranch } = params;
  const router = useRouter();
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));

  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // State for tables and UI
  const [tables, setTables] = useState<Table[]>(mockTables);
  const [floors, setFloors] = useState<Floor[]>(defaultFloors);
  const [areas, setAreas] = useState<Area[]>(defaultAreas);
  const [selectedFloor, setSelectedFloor] = useState('floor-1');
  const [selectedArea, setSelectedArea] = useState('dining');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedAreaForEdit, setSelectedAreaForEdit] = useState<Area | null>(null);
  const [selectedFloorForEdit, setSelectedFloorForEdit] = useState<Floor | null>(null);
  const [isAddTableOpen, setIsAddTableOpen] = useState(false);
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [isManageAreasOpen, setIsManageAreasOpen] = useState(false);
  const [isAddAreaOpen, setIsAddAreaOpen] = useState(false);
  const [isEditAreaOpen, setIsEditAreaOpen] = useState(false);
  const [isDeleteAreaOpen, setIsDeleteAreaOpen] = useState(false);
  const [isManageFloorsOpen, setIsManageFloorsOpen] = useState(false);
  const [isAddFloorOpen, setIsAddFloorOpen] = useState(false);
  const [isEditFloorOpen, setIsEditFloorOpen] = useState(false);
  const [isDeleteFloorOpen, setIsDeleteFloorOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement>(null);

  // Form state for adding/editing tables
  const [tableForm, setTableForm] = useState({
    number: 1,
    capacity: 2,
    shape: 'square',
    width: 80,
    height: 80,
  });

  // Form state for adding/editing areas
  const [areaForm, setAreaForm] = useState({
    name: '',
    floorId: selectedFloor,
  });

  // Form state for adding/editing floors
  const [floorForm, setFloorForm] = useState({
    name: '',
    order: 1,
  });

  // Filter areas by selected floor
  const filteredAreas = areas.filter(area => area.floorId === selectedFloor);

  // Filter tables by selected area
  const filteredTables = tables.filter(table => table.location === selectedArea);

  // Handle table selection
  const handleTableClick = (table: Table, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedTable(table);
  };

  // Handle canvas click (deselect table)
  const handleCanvasClick = () => {
    setSelectedTable(null);
  };

  // Handle table drag start (works for both mouse and touch)
  const handleDragStart = (e: React.MouseEvent | React.TouchEvent, table: Table) => {
    e.stopPropagation();
    setIsDragging(true);
    setSelectedTable(table);

    // Calculate offset from pointer position to table position
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    setDragOffset({
      x: clientX - rect.left,
      y: clientY - rect.top,
    });
  };

  // Handle pointer move for dragging (works for both mouse and touch)
  const handlePointerMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !selectedTable || !canvasRef.current) return;

    // Get canvas bounds
    const canvasRect = canvasRef.current.getBoundingClientRect();

    // Get client coordinates (works for both mouse and touch)
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    // Calculate new position
    const newX = clientX - canvasRect.left - dragOffset.x;
    const newY = clientY - canvasRect.top - dragOffset.y;

    // Ensure table stays within canvas bounds
    const boundedX = Math.max(0, Math.min(newX, canvasRect.width - (selectedTable.width || 80)));
    const boundedY = Math.max(0, Math.min(newY, canvasRect.height - (selectedTable.height || 80)));

    // Update table position
    setTables(tables.map(table =>
      table.id === selectedTable.id
        ? { ...table, positionX: boundedX, positionY: boundedY }
        : table
    ));
  };

  // Handle pointer up to end dragging (works for both mouse and touch)
  const handlePointerUp = () => {
    if (isDragging) {
      setIsDragging(false);
      // In a real app, we would save the updated position to the API
      toast.success('Table position updated');
    }
  };

  // Handle adding a new table
  const handleAddTable = () => {
    const newTable: Table = {
      id: `new-${Date.now()}`,
      number: Math.max(0, ...tables.map(t => t.number)) + 1,
      capacity: tableForm.capacity,
      status: 'available',
      location: selectedArea,
      shape: tableForm.shape,
      positionX: 100,
      positionY: 100,
      width: tableForm.width,
      height: tableForm.height,
    };

    setTables([...tables, newTable]);
    setIsAddTableOpen(false);
    setTableForm({
      number: newTable.number + 1,
      capacity: 2,
      shape: 'square',
      width: 80,
      height: 80,
    });

    toast.success('Table added successfully');
  };

  // Handle editing a table
  const handleEditTable = () => {
    if (!selectedTable) return;

    setTables(tables.map(table =>
      table.id === selectedTable.id
        ? {
            ...table,
            capacity: tableForm.capacity,
            shape: tableForm.shape,
            width: tableForm.width,
            height: tableForm.height,
          }
        : table
    ));

    setIsEditTableOpen(false);
    toast.success('Table updated successfully');
  };

  // Handle deleting a table
  const handleDeleteTable = () => {
    if (!selectedTable) return;

    setTables(tables.filter(table => table.id !== selectedTable.id));
    setSelectedTable(null);
    toast.success('Table deleted successfully');
  };

  // Open edit dialog
  const openEditDialog = () => {
    if (!selectedTable) return;

    setTableForm({
      number: selectedTable.number,
      capacity: selectedTable.capacity,
      shape: selectedTable.shape,
      width: selectedTable.width,
      height: selectedTable.height,
    });

    setIsEditTableOpen(true);
  };

  // Save layout
  const saveLayout = () => {
    // In a real app, we would save all tables and areas to the API
    toast.success('Layout saved successfully');
  };

  if (!branchWithShop) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-3 sm:p-6 font-be-vietnam">
      {/* Header - Responsive for mobile */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-[#181510]">Table Layout Editor</h1>
          <p className="text-[#8a745c] text-sm">Design your restaurant floor plan for {shop.name} - {branch.name}</p>
        </div>
        <div className="flex flex-wrap gap-2 sm:gap-3 w-full sm:w-auto">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`} className="w-1/2 sm:w-auto">
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510] w-full sm:w-auto">
              <ArrowLeft className="h-4 w-4 mr-2 hidden sm:inline" />
              Back
            </Button>
          </Link>
          <Button
            className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-1/2 sm:w-auto"
            onClick={saveLayout}
          >
            <Save className="h-4 w-4 mr-2 hidden sm:inline" />
            Save Layout
          </Button>
        </div>
      </div>

      {/* Floor and Area Selection */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <label className="block text-sm font-medium text-[#181510] mb-2">Floor</label>
          <Select value={selectedFloor} onValueChange={setSelectedFloor}>
            <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
              <SelectValue placeholder="Select floor" />
            </SelectTrigger>
            <SelectContent>
              {floors.map((floor) => (
                <SelectItem key={floor.id} value={floor.id}>
                  {floor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium text-[#181510] mb-2">Area</label>
          <Select value={selectedArea} onValueChange={setSelectedArea}>
            <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
              <SelectValue placeholder="Select area" />
            </SelectTrigger>
            <SelectContent>
              {filteredAreas.map((area) => (
                <SelectItem key={area.id} value={area.id}>
                  {area.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Main content - Responsive grid layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Sidebar - Areas and Selected Table */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          {/* Add Table Button */}
          <div className="mb-4">
            <Dialog open={isAddTableOpen} onOpenChange={setIsAddTableOpen}>
              <DialogTrigger asChild>
                <Button className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Table
                </Button>
              </DialogTrigger>
              <DialogContent className="bg-[#fbfaf9]">
                <DialogHeader>
                  <DialogTitle>Add New Table</DialogTitle>
                  <DialogDescription>
                    Create a new table for the {filteredAreas.find(a => a.id === selectedArea)?.name} area.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Capacity</label>
                    <Input
                      type="number"
                      min="1"
                      max="20"
                      value={tableForm.capacity}
                      onChange={(e) => setTableForm({ ...tableForm, capacity: parseInt(e.target.value) || 2 })}
                      className="bg-[#fbfaf9] border-[#e2dcd4]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-[#181510] mb-2">Shape</label>
                    <Select value={tableForm.shape} onValueChange={(value) => setTableForm({ ...tableForm, shape: value })}>
                      <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {tableShapes.map((shape) => (
                          <SelectItem key={shape.id} value={shape.id}>
                            <div className="flex items-center gap-2">
                              {shape.icon}
                              {shape.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Width</label>
                      <Input
                        type="number"
                        min="40"
                        max="200"
                        value={tableForm.width}
                        onChange={(e) => setTableForm({ ...tableForm, width: parseInt(e.target.value) || 80 })}
                        className="bg-[#fbfaf9] border-[#e2dcd4]"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-[#181510] mb-2">Height</label>
                      <Input
                        type="number"
                        min="40"
                        max="200"
                        value={tableForm.height}
                        onChange={(e) => setTableForm({ ...tableForm, height: parseInt(e.target.value) || 80 })}
                        className="bg-[#fbfaf9] border-[#e2dcd4]"
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <DialogClose asChild>
                    <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button onClick={handleAddTable} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                    Add Table
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          {/* Selected Table Info */}
          {selectedTable && (
            <div className="bg-[#f1edea] p-3 sm:p-4 rounded-lg mb-4 sm:mb-6">
              <h3 className="text-base sm:text-lg font-medium text-[#181510] mb-3">Table {selectedTable.number}</h3>
              <div className="space-y-2 text-sm text-[#8a745c]">
                <div>Capacity: {selectedTable.capacity} people</div>
                <div>Shape: {selectedTable.shape}</div>
                <div>Status: {selectedTable.status}</div>
              </div>
              <div className="flex gap-2 mt-4">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={openEditDialog}
                  className="border-[#e2dcd4] text-[#181510] flex-1"
                >
                  <Edit className="h-3 w-3 mr-1" />
                  Edit
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleDeleteTable}
                  className="border-red-300 text-red-600 hover:bg-red-50"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Canvas - Table Layout */}
        <div className="lg:col-span-3 order-1 lg:order-2">
          <div className="bg-[#fbfaf9] border-2 border-dashed border-[#e2dcd4] rounded-lg p-4 min-h-[400px] sm:min-h-[600px] relative overflow-hidden">
            <div
              ref={canvasRef}
              className="w-full h-full relative cursor-crosshair"
              onClick={handleCanvasClick}
              onMouseMove={handlePointerMove}
              onMouseUp={handlePointerUp}
              onTouchMove={handlePointerMove}
              onTouchEnd={handlePointerUp}
            >
              {/* Grid background */}
              <div className="absolute inset-0 opacity-20">
                <svg width="100%" height="100%">
                  <defs>
                    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                      <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#8a745c" strokeWidth="0.5"/>
                    </pattern>
                  </defs>
                  <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
              </div>

              {/* Tables */}
              {filteredTables.map((table) => (
                <div
                  key={table.id}
                  className={`absolute cursor-move select-none transition-all duration-200 ${
                    selectedTable?.id === table.id
                      ? 'ring-2 ring-[#8a745c] ring-offset-2'
                      : 'hover:ring-1 hover:ring-[#8a745c] hover:ring-offset-1'
                  }`}
                  style={{
                    left: table.positionX,
                    top: table.positionY,
                    width: table.width,
                    height: table.height,
                  }}
                  onClick={(e) => handleTableClick(table, e)}
                  onMouseDown={(e) => handleDragStart(e, table)}
                  onTouchStart={(e) => handleDragStart(e, table)}
                >
                  {/* Table shape */}
                  <div
                    className={`w-full h-full bg-[#e5ccb2] border-2 border-[#8a745c] flex items-center justify-center text-[#181510] font-medium text-sm ${
                      table.shape === 'circle' ? 'rounded-full' : table.shape === 'rectangle' ? 'rounded-lg' : 'rounded-md'
                    }`}
                  >
                    <div className="text-center">
                      <div className="font-bold">{table.number}</div>
                      <div className="text-xs opacity-75">{table.capacity}p</div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Empty state */}
              {filteredTables.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-[#8a745c]">
                    <LayoutGrid className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium mb-2">No tables in this area</p>
                    <p className="text-sm">Click "Add Table" to start designing your layout</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Edit Table Dialog */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent className="bg-[#fbfaf9]">
          <DialogHeader>
            <DialogTitle>Edit Table {selectedTable?.number}</DialogTitle>
            <DialogDescription>
              Modify the table properties.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-[#181510] mb-2">Capacity</label>
              <Input
                type="number"
                min="1"
                max="20"
                value={tableForm.capacity}
                onChange={(e) => setTableForm({ ...tableForm, capacity: parseInt(e.target.value) || 2 })}
                className="bg-[#fbfaf9] border-[#e2dcd4]"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-[#181510] mb-2">Shape</label>
              <Select value={tableForm.shape} onValueChange={(value) => setTableForm({ ...tableForm, shape: value })}>
                <SelectTrigger className="bg-[#fbfaf9] border-[#e2dcd4]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {tableShapes.map((shape) => (
                    <SelectItem key={shape.id} value={shape.id}>
                      <div className="flex items-center gap-2">
                        {shape.icon}
                        {shape.name}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Width</label>
                <Input
                  type="number"
                  min="40"
                  max="200"
                  value={tableForm.width}
                  onChange={(e) => setTableForm({ ...tableForm, width: parseInt(e.target.value) || 80 })}
                  className="bg-[#fbfaf9] border-[#e2dcd4]"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-[#181510] mb-2">Height</label>
                <Input
                  type="number"
                  min="40"
                  max="200"
                  value={tableForm.height}
                  onChange={(e) => setTableForm({ ...tableForm, height: parseInt(e.target.value) || 80 })}
                  className="bg-[#fbfaf9] border-[#e2dcd4]"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
                Cancel
              </Button>
            </DialogClose>
            <Button onClick={handleEditTable} className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
