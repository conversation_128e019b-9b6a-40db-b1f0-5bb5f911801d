'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { <PERSON>, Bell } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

// Mock table data with images
const mockTables = [
  {
    id: '1',
    number: 1,
    name: 'Table 1',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBitRJJIAwrsT3sN69YFrCfepOrR3pneJ4inWpwR7iu2tNMseZE90YE_oCNgkmq5Gs_UyUyocZX_daSu7NymBhRzfJNJRLDw3DRHhJ3HgXSSPZMyBTglltX4eAZaQ3guLhNiLPkYbwwmj77cLZogMsBiBm-6X4wKpxb-20L1PlcjmWmV_OTwZFlsVy_Q-52Bw-nvA7xF1H6egrCjRuyBkQz_H9jh_Ooe-9XgXYdUb8yvdgkJPgPXxmjpmHDFdx4aHichBxyNhdEo8WW',
    location: 'dining'
  },
  {
    id: '2',
    number: 2,
    name: 'Table 2',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCm5kKI8V-CR8Cq_8PQH4XUAiPQirzRWlhKic0WW1RETxcRK-_Ar3CJjWXGUmL7zwdy0fhyEb6uKSin4etsZO72RQ-9anPe5RnTyJSuMSPntdFdejZEVZCTQ_of2HgVS4wiqf8wkmwzT7PbrONDjcdli0eXZURLYp0iRgXrSndH8i-wLDieR9ZonyQMrMiOd0zVNzsV1Lf0lHlTAcamZTZmhz66EcDm0Y-LT-gU29Liy1qhDb3ykMOCMrobBocxOy1uLa8lhYkp8qSj',
    location: 'dining'
  },
  {
    id: '3',
    number: 3,
    name: 'Table 3',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCmluEqeoS8maaJInF5ENft68j3Zo6lrjuN6Cz7EX7tmiurjpgiPC9qFmQ7ouTL5CYHEClO4_K_Gm8g1Ez2N5hvb-EnIEFj6yrbX9nR6oPzI7jpKCsiZ4uhOV0-0s_7FYdmG_jOntuziVZiVzaA7k34MrhfEHBiKeCWcrS_eGHx_0hIdpIeWlTAgRvihrqcs4H0aBkPRqRnQ-JoHezoQJkBq1MANw1b93TUOfADf3916wSitfLZ5S0LOvSeK3SOJBSoTJc8gV0LiQOV',
    location: 'dining'
  },
  {
    id: '4',
    number: 4,
    name: 'Table 4',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDeKZLfgmskuVwyKS6M2mqm-wHKoVsx8DUwJ6orJ3HRCmqVzBBNb18hK6B3-rHkTmyFU9u8I67VNnvnp8zp-7hjz2q2sw57FOn4Xg9fWvMpmBSfcyogsig7XCGMWkWIsSw9QkY275_LLly2gW72XfWBGIOXvA9IYAg3lhMMczeNRmF5CKp7-kNIRydPwoUlRyFJzDLnsznrY0rgP3JsjxN775-duLZB5pHV26d8CEz-9Kybxe25OBNE9Lr--B8ghsYf4AYEgSA1NpoX',
    location: 'dining'
  },
  {
    id: '5',
    number: 5,
    name: 'Table 5',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBaYcUah5GnpaX-LjIAnsnx7b_FGm530dKhaX9qcIR5xbMeZb5Jb4PTg2Mqiz5mheEjIXkdPnwWHbYeoeyRYeKxxhlxkQo9iflGzKG4oD2YuzuONa-DMQBc4F9iDEfO7F_AqIZ6Hb4nyc7J7ATNBgcrfduzhToQ5uH7YMzcAe-yNk7K5TiQAJxiSJY_EhzZTlliY7ThA4olnMU74LqlKQ9Q4lKXSgUdtLGqwf5n05eG84-4F4qcmFrkvwv-SPac7NG4U6KgcIkdXR7U',
    location: 'dining'
  },
  {
    id: '6',
    number: 6,
    name: 'Table 6',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuC3UPgd7rhRsDT7eYfAD2T-sPufNEqRrHt5F6dHFgUXuNokqidPvPIdhj3XI4faNbEaoQlvdtuFn-U7t4dkbq_RrUyNsUkfgNJKCNFnixLjqvF5_7YNHuqbYpeBlzoY6EK4p9pyznahnSoHr8XMpikZC6_g1xVmvHna5tEadf1ov8--gq51t7rzp6KSCrCsgdx54NAfSyPmCmhNYUS0OAmH5zuk7wG3Y6OAKzF-Hukya2ehV8z7_1fMbUH4yNw7dugwyru9is9ljIPo',
    location: 'dining'
  },
  {
    id: '7',
    number: 7,
    name: 'Table 7',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuD9HQ9qBH6E7N3BPxRfx1bJXYd-TZZ_6SLmdly5C6k5wNtgkYmpZG06l39IEz_Ia2tiCQi5BdrFigYqg8Gpq1-xI1WgDzGmg0Qat3AfwRURE5qm-65to1Zi0EgrXtBdCYGw5g1s3l2NWWfQer0oRvEIDKXnmBOeO_CecYUgeei8goDhIbiUOuPqv-UNzHQ1B2o_WHSFIi-SHIgK71I9qBSRNkJprUpW8Xk75UNFiMhj8zZT8o1YWmVw-yeT7RkO3KnNzBXDBfMrjPGk',
    location: 'dining'
  },
  {
    id: '8',
    number: 8,
    name: 'Table 8',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuAbEbLgXmmGsSE-rc6BgB_EHvxJYYfk_q1Q6LdxMjBBZusJOu-SR5Lj9U-j1thggcac0GlBkzjriHuqAJT8rU1zn0t6rhOecLtktvA-gqb__BzGsL4DPIDmo66KCH7G_kt8dpUhYOYQhCgBJVcpHR2bqnsGYutNIJydMCAbtVyRHMFMhaMmLqAnywhsFVUeh1wyjBNKXhw8DSNRkWVYCF3b_zUoQhGV-Y1xeEwwJ3uJGuTJanIbsc8W4lr-_fMslYcr_s7uWXg6_jRB',
    location: 'dining'
  },
  {
    id: '9',
    number: 9,
    name: 'Table 9',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCIz0HXWiDXpcBAoN5tkRxMfmuAZEr_bR5i1v-LJGz3rJ3bvTCxwd8-zdvsbOZZ846ZRH69lPKTQiW5NhJbP_-e3T_xnLqjmYDVrm43IqtOt6vDblBqxeC5_gYUT2gwS-pdLP3TnRxdGJIHEbW3ExL4uUJW4Em8SGOCPccBfy_FSmLzYZDwWJ_GojPr5zBp0JbwDDs2nR2aN2eiI385VFdl4nezOKcrRdmm1PrhMYjFoOHAE5eczLj91yOzHwZbmIgJJK7l5xSXD7eC',
    location: 'dining'
  },
  {
    id: '10',
    number: 10,
    name: 'Table 10',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBOGlr9W9KBrS6VSv8V5HCE6ylasPrD1NvE3mMs9AB2hhRc-2lZbrQivuaDLwLoe2I9mGFwJ1LFXVof7D8u-eu_9l3XGCfh4y2Sbi4OA-hdCU45bq0R7yRdaavSiIuVcseKMzxbcJ8q6Q482YXBk2DqSLE0Zi34Ylbbdc-mhH0QwXgIQXlS55M3aporIXMXJQ_-87FmDSUwDZ2jB04dihpgs-zOGDxvIj9qWKdGNNzBkvgy3TjFzGDAExf_Dic6ZUJQdnAAvcxTc6j',
    location: 'outdoor'
  },
  {
    id: '11',
    number: 11,
    name: 'Table 11',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuDQKknOb2zf7QS-q5HVjzt6C9rVLYuiEImsgWF3cHQ1DIe3pwEDkS52hck_SgugsNk5kbogoXGY-MZpIZF2dGDZzE2m701-wJWBM6U-uZbGAKdxUsd2hSKZ1pSk195CtMz850v40dZ0z5TPpuOQVWfwrE4RkYji0Jaf7G6cxE02JCooay55mNHT28dHhBT0NisuKVZ0lDnd7juDMYSTttW2hEYNGt7yffWfjwZYeVVyEJlyH2XhBE8p90m1aftxmiNF5FKyOazJyKRc',
    location: 'outdoor'
  },
  {
    id: '12',
    number: 12,
    name: 'Table 12',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBPOpsvPGLf0WjPng7RmEMaYXivOfOM2lai3svFLXM04vqptsNAX9uxiWvS-o_NEOdNNIaKb2DKm75VHtfI3r9zTGyXzgXxEzXohtWdRDak4gLqLk548iHHZqPMFKqTLTxVXcmc6uNgV2-y9RuxQJn4pIMflICpykz44_m7-cTlT-EgieVnPwgCOTLvyUxfsrckXlbi8Cdl_ycO--899rs5YXfncTlBEC3GEt5KDUCrjJyx4VJRnjWgwjwRvs6bqPmazyRTqvKIT8QB',
    location: 'outdoor'
  },
  {
    id: '13',
    number: 13,
    name: 'Table 13',
    image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBYiexir2spyoRDU3_rkmQBrbjy6urJ2_sWfYE0qreHXkmTuT2tkefw9kzRmZrjD69ySwjLf33iP_H5ZqCiRDlsWX2GCYE1ISYFZ1nBl6h56DaWb_wIQJ86ClIsL5xr9IpJ45vkG4Vn8OYfXx86M2zs-R7sc-PcWx62h0HTIQYOJCTvSP9TcYFb-Ap1ASfJ98acBSq-TlqXKrIquDlTdbkkATyF_Qy71BzuRznevhEI4VrBPYzJn_HjNdIj9bPg53WJI5c78GMn86-e',
    location: 'outdoor'
  }
];

interface TablesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function TablesPage({ params }: TablesPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('floor-plan');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] group/design-root overflow-x-hidden font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter tables by location
  const diningTables = mockTables.filter(table => table.location === 'dining');
  const outdoorTables = mockTables.filter(table => table.location === 'outdoor');

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        {/* Header */}
        <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#f1edea] px-10 py-3">
          <div className="flex items-center gap-4 text-[#181510]">
            <div className="size-4">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                  fill="currentColor"
                />
              </svg>
            </div>
            <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">Table Manager</h2>
          </div>
          <div className="flex flex-1 justify-end gap-8">
            <label className="flex flex-col min-w-40 !h-10 max-w-64">
              <div className="flex w-full flex-1 items-stretch rounded-lg h-full">
                <div className="text-[#8a745c] flex border-none bg-[#f1edea] items-center justify-center pl-4 rounded-l-lg border-r-0">
                  <Search size={24} />
                </div>
                <input
                  placeholder="Search"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  value=""
                />
              </div>
            </label>
            <button className="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 bg-[#f1edea] text-[#181510] gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5">
              <Bell size={20} />
            </button>
            <div
              className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10"
              style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuBTI0Z2xPUTyhRVeL1K9vLjQGbi-1UHxVlqwNVDF-iTr92vrWHqqemi73fpc8wOTxMsEWHp2sMWTYeuRGEAvkoprEmdcqQKjv1ysjZPz-Oxjj0nwmNth_9fIg4-n1G3JRRNt6U7R2hsx-S0tKSQkNScC_IoX_R82wZ86pqUnzHoPxbvYmN3_czqGASpoFbwwL2zK_CrjOxPpt8-qcbimmno5Mh8rjWKzJy07NeVJl7_QAhgvUVft3cXpPUn8wHTLxZSJXRyU2pkKbK3")'}}
            />
          </div>
        </header>

        <div className="px-40 flex flex-1 justify-center py-5">
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Page Header */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Table Layout</p>
                <p className="text-[#8a745c] text-sm font-normal leading-normal">Manage your restaurant's table layout and reservations.</p>
              </div>
            </div>

            {/* Tabs */}
            <div className="pb-3">
              <div className="flex border-b border-[#e2dcd4] px-4 gap-8">
                <button
                  onClick={() => setActiveTab('floor-plan')}
                  className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                    activeTab === 'floor-plan'
                      ? 'border-b-[#e5ccb2] text-[#181510]'
                      : 'border-b-transparent text-[#8a745c]'
                  }`}
                >
                  <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                    activeTab === 'floor-plan' ? 'text-[#181510]' : 'text-[#8a745c]'
                  }`}>Floor Plan</p>
                </button>
                <button
                  onClick={() => setActiveTab('reservations')}
                  className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                    activeTab === 'reservations'
                      ? 'border-b-[#e5ccb2] text-[#181510]'
                      : 'border-b-transparent text-[#8a745c]'
                  }`}
                >
                  <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                    activeTab === 'reservations' ? 'text-[#181510]' : 'text-[#8a745c]'
                  }`}>Reservations</p>
                </button>
                <button
                  onClick={() => setActiveTab('waitlist')}
                  className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                    activeTab === 'waitlist'
                      ? 'border-b-[#e5ccb2] text-[#181510]'
                      : 'border-b-transparent text-[#8a745c]'
                  }`}
                >
                  <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                    activeTab === 'waitlist' ? 'text-[#181510]' : 'text-[#8a745c]'
                  }`}>Waitlist</p>
                </button>
              </div>
            </div>

            {/* Floor Plan Tab Content */}
            {activeTab === 'floor-plan' && (
              <>
                {/* Dining Area */}
                <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Dining Area</h2>
                <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                  {diningTables.map((table) => (
                    <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                      <div
                        className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square cursor-pointer hover:opacity-80 transition-opacity"
                        style={{
                          backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                        }}
                      >
                        <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">{table.name}</p>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Outdoor Patio */}
                <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Outdoor Patio</h2>
                <div className="grid grid-cols-[repeat(auto-fit,minmax(158px,1fr))] gap-3 p-4">
                  {outdoorTables.map((table) => (
                    <Link key={table.id} href={`/app/restaurant/${slugShop}/${slugBranch}/tables/${table.id}`}>
                      <div
                        className="bg-cover bg-center flex flex-col gap-3 rounded-lg justify-end p-4 aspect-square cursor-pointer hover:opacity-80 transition-opacity"
                        style={{
                          backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%, rgba(0, 0, 0, 0.1) 100%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                        }}
                      >
                        <p className="text-white text-base font-bold leading-tight w-4/5 line-clamp-2">{table.name}</p>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="flex justify-stretch">
                  <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-end">
                    <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
                      <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#f1edea] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e2dcd4] transition-colors">
                        <span className="truncate">Edit Layout</span>
                      </button>
                    </Link>
                    <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#e5ccb2] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d6bd9e] transition-colors">
                      <span className="truncate">Add Reservation</span>
                    </button>
                  </div>
                </div>
              </>
            )}

            {/* Reservations Tab Content */}
            {activeTab === 'reservations' && (
              <div className="p-4 text-center text-[#8a745c]">
                <p>Reservations feature coming soon</p>
              </div>
            )}

            {/* Waitlist Tab Content */}
            {activeTab === 'waitlist' && (
              <div className="p-4 text-center text-[#8a745c]">
                <p>Waitlist feature coming soon</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
