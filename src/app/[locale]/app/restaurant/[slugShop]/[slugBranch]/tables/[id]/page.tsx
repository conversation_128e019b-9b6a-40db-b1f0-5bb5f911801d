'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { ArrowLeft, QrCode, Users, Clock, Calendar } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

// Mock table data
const mockTableDetail = {
  id: '1',
  number: 1,
  name: 'Table 1',
  capacity: 4,
  status: 'available',
  location: 'dining',
  image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuBitRJJIAwrsT3sN69YFrCfepOrR3pneJ4inWpwR7iu2tNMseZE90YE_oCNgkmq5Gs_UyUyocZX_daSu7NymBhRzfJNJRLDw3DRHhJ3HgXSSPZMyBTglltX4eAZaQ3guLhNiLPkYbwwmj77cLZogMsBiBm-6X4wKpxb-20L1PlcjmWmV_OTwZFlsVy_Q-52Bw-nvA7xF1H6egrCjRuyBkQz_H9jh_Ooe-9XgXYdUb8yvdgkJPgPXxmjpmHDFdx4aHichBxyNhdEo8WW',
  qrCode: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://restaurant.example.com/table/1',
  description: 'A comfortable 4-seat table in the main dining area with a great view of the restaurant.',
  features: ['Window view', 'Near kitchen', 'Wheelchair accessible'],
  currentReservation: null,
  nextReservation: {
    id: 'res1',
    customerName: 'John Doe',
    time: '19:00',
    date: '2024-01-15',
    partySize: 4,
    status: 'confirmed'
  }
};

interface TableDetailPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    id: string;
  }>;
}

export default function TableDetailPage({ params }: TableDetailPageProps) {
  const { slugShop, slugBranch, id } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [table] = useState(mockTableDetail);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'occupied':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'reserved':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <>
      {/* Back Button */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables`}>
          <button className="flex items-center gap-2 text-[#8a745c] hover:text-[#181510] transition-colors">
            <ArrowLeft size={20} />
            <span className="text-sm font-medium">Back to Tables</span>
          </button>
        </Link>
      </div>

      {/* Page Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">{table.name}</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Table details and management for {table.name}
          </p>
        </div>
        <div className="flex items-start gap-3">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(table.status)}`}>
            {table.status.charAt(0).toUpperCase() + table.status.slice(1)}
          </span>
        </div>
      </div>

      {/* Table Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Table Image */}
        <div className="space-y-4">
          <h2 className="text-[#181510] text-lg font-semibold">Table View</h2>
          <div
            className="w-full h-64 bg-cover bg-center rounded-lg"
            style={{
              backgroundImage: `url("${table.image}")`
            }}
          />
        </div>

        {/* Table Information */}
        <div className="space-y-4">
          <h2 className="text-[#181510] text-lg font-semibold">Table Information</h2>
          <div className="bg-white border border-[#e2dcd4] rounded-lg p-4 space-y-4">
            <div className="flex items-center gap-3">
              <Users size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Capacity</p>
                <p className="text-[#181510] font-medium">{table.capacity} guests</p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <Calendar size={20} className="text-[#8a745c]" />
              <div>
                <p className="text-sm text-[#8a745c]">Location</p>
                <p className="text-[#181510] font-medium">{table.location === 'dining' ? 'Dining Area' : 'Outdoor Patio'}</p>
              </div>
            </div>

            {table.nextReservation && (
              <div className="flex items-center gap-3">
                <Clock size={20} className="text-[#8a745c]" />
                <div>
                  <p className="text-sm text-[#8a745c]">Next Reservation</p>
                  <p className="text-[#181510] font-medium">
                    {table.nextReservation.customerName} at {table.nextReservation.time}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* QR Code Section */}
      <div className="bg-white border border-[#e2dcd4] rounded-lg p-6 mb-8">
        <h2 className="text-[#181510] text-lg font-semibold mb-4">QR Code</h2>
        <div className="flex flex-col md:flex-row items-start gap-6">
          <div className="flex-shrink-0">
            <img
              src={table.qrCode}
              alt={`QR Code for ${table.name}`}
              className="w-32 h-32 border border-[#e2dcd4] rounded-lg"
            />
          </div>
          <div className="flex-1">
            <p className="text-[#8a745c] text-sm mb-4">
              Customers can scan this QR code to view the menu and place orders directly from their table.
            </p>
            <button className="flex items-center gap-2 bg-[#e58219] text-white px-4 py-2 rounded-lg hover:bg-[#d4741a] transition-colors">
              <QrCode size={16} />
              <span className="text-sm font-medium">Download QR Code</span>
            </button>
          </div>
        </div>
      </div>

      {/* Table Features */}
      {table.features && table.features.length > 0 && (
        <div className="bg-white border border-[#e2dcd4] rounded-lg p-6 mb-8">
          <h2 className="text-[#181510] text-lg font-semibold mb-4">Features</h2>
          <div className="flex flex-wrap gap-2">
            {table.features.map((feature, index) => (
              <span
                key={index}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-[#f1edea] text-[#181510]"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#f1edea] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#e2dcd4] transition-colors">
          <span className="truncate">Edit Table</span>
        </button>
        <button className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-lg h-10 px-4 bg-[#e5ccb2] text-[#181510] text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#d6bd9e] transition-colors">
          <span className="truncate">Add Reservation</span>
        </button>
      </div>
    </>
  );
}
