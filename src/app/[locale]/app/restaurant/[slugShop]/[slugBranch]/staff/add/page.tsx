'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Plus } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { getBranchWithShop } from '@/mock/shopData';
import { generateSlug } from '@/lib/utils';
import { toast } from 'sonner';

interface StaffAddPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
  };
}

export default function StaffAddPage({ params }: StaffAddPageProps) {
  const { slugShop, slugBranch } = params;
  const router = useRouter();
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    email: '',
    phone: '',
    username: '',
    password: '',
    permissions: [] as string[],
  });

  // Available permissions
  const availablePermissions = [
    'Manage Orders',
    'Update Menu',
    'View Reports',
    'Manage Staff'
  ];

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const togglePermission = (permission: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.name) {
        toast.error('Name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.email) {
        toast.error('Email is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.role) {
        toast.error('Role is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.username) {
        toast.error('Username is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.password) {
        toast.error('Password is required');
        setIsSubmitting(false);
        return;
      }

      // Generate slug from name
      const slug = generateSlug(formData.name);

      // In a real app, we would call the API to create the staff member
      // const response = await createStaff({ ...formData, slug }).unwrap();

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Staff member added successfully');

      // Redirect to staff list
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/staff`);
    } catch (error) {
      toast.error('Failed to add staff member');
      console.error('Error adding staff member:', error);
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Add New Staff Member</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Add a new team member to {shop.name} - {branch.name}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-[512px] space-y-6">
        {/* Full Name */}
        <div className="space-y-2">
          <Label htmlFor="name" className="text-[#181511] text-base font-medium leading-normal text-left">Full Name</Label>
          <Input
            id="name"
            placeholder="Enter full name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
        </div>

        {/* Role */}
        <div className="space-y-2">
          <Label htmlFor="role" className="text-[#181511] text-base font-medium leading-normal text-left">Role</Label>
          <Select value={formData.role} onValueChange={(value) => handleChange('role', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="chef">Chef</SelectItem>
              <SelectItem value="server">Server</SelectItem>
              <SelectItem value="bartender">Bartender</SelectItem>
              <SelectItem value="hostess">Hostess</SelectItem>
              <SelectItem value="manager">Manager</SelectItem>
              <SelectItem value="cashier">Cashier</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Contact Number */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-[#181511] text-base font-medium leading-normal text-left">Contact Number</Label>
          <Input
            id="phone"
            placeholder="Enter contact number"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
          />
        </div>

        {/* Email Address */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-[#181511] text-base font-medium leading-normal text-left">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter email address"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            required
          />
        </div>

        {/* Username */}
        <div className="space-y-2">
          <Label htmlFor="username" className="text-[#181511] text-base font-medium leading-normal text-left">Username</Label>
          <Input
            id="username"
            placeholder="Create username"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.username}
            onChange={(e) => handleChange('username', e.target.value)}
            required
          />
        </div>

        {/* Password */}
        <div className="space-y-2">
          <Label htmlFor="password" className="text-[#181511] text-base font-medium leading-normal text-left">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Create password"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.password}
            onChange={(e) => handleChange('password', e.target.value)}
            required
          />
        </div>

        {/* Permissions */}
        <div className="space-y-3">
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">Permissions</h3>
          <div className="flex gap-3 flex-wrap">
            {availablePermissions.map((permission) => (
              <button
                key={permission}
                type="button"
                onClick={() => togglePermission(permission)}
                className={`flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full pl-4 pr-4 transition-colors ${
                  formData.permissions.includes(permission)
                    ? 'bg-[#e58219] text-white'
                    : 'bg-[#f4f2f0] text-[#181511] hover:bg-[#e5e1dc]'
                }`}
              >
                <p className="text-sm font-medium leading-normal">{permission}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button type="button" variant="outline" className="border-[#e2dcd4] text-[#181510]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] hover:bg-[#d4741a] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
          >
            <span className="truncate">
              {isSubmitting ? 'Adding...' : 'Add Staff Member'}
            </span>
          </Button>
        </div>
      </form>
    </div>
  );
}
