'use client';

import { useState } from 'react';
import { <PERSON> } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Download, FileText, Award, Calendar, Clock, TrendingUp, Users } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';

// Mock staff data with comprehensive details
const mockStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    slug: 'ethan-carter',
    staffId: '12345',
    role: 'Chef',
    department: 'Kitchen',
    status: 'active',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop',
    dateOfBirth: '1985-03-15',
    gender: 'Male',
    nationality: 'American',
    address: '456 Oak Avenue',
    city: 'New York',
    postalCode: '10002',
    country: 'USA',
    permissions: ['Manage Kitchen', 'View Reports'],
    startDate: '2020-03-15',
    emergencyContactName: 'Sarah Carter',
    emergencyContactNumber: '+****************',
    schedule: [
      { day: 'Monday', startTime: '9:00 AM', endTime: '5:00 PM', status: 'Scheduled' },
      { day: 'Tuesday', startTime: '9:00 AM', endTime: '5:00 PM', status: 'Scheduled' },
      { day: 'Wednesday', startTime: '9:00 AM', endTime: '5:00 PM', status: 'Scheduled' },
      { day: 'Thursday', startTime: '9:00 AM', endTime: '5:00 PM', status: 'Scheduled' },
      { day: 'Friday', startTime: '9:00 AM', endTime: '5:00 PM', status: 'Scheduled' },
      { day: 'Saturday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Sunday', startTime: 'Off', endTime: 'Off', status: 'Off' },
    ],
    performance: {
      averageOrderTime: '12 min',
      customerSatisfaction: '98%',
      attendanceRate: '100%',
      ordersCompleted: 1250,
      monthlyRating: 4.9,
      teamCollaboration: '95%',
      punctuality: '100%',
      skillLevel: 'Expert',
      trainingHours: 120,
    },
    documents: [
      { id: 1, name: 'Employment Contract', type: 'PDF', uploadDate: '2020-03-15', size: '2.4 MB' },
      { id: 2, name: 'Food Safety Certificate', type: 'PDF', uploadDate: '2023-01-10', size: '1.2 MB' },
      { id: 3, name: 'Background Check', type: 'PDF', uploadDate: '2020-03-10', size: '0.8 MB' },
      { id: 4, name: 'Training Records', type: 'PDF', uploadDate: '2023-12-01', size: '3.1 MB' },
    ],
    recentActivity: [
      { date: '2024-01-15', action: 'Completed shift', details: 'Kitchen - Evening shift' },
      { date: '2024-01-14', action: 'Training completed', details: 'Advanced Culinary Techniques' },
      { date: '2024-01-13', action: 'Performance review', details: 'Quarterly review - Excellent' },
      { date: '2024-01-12', action: 'Shift covered', details: 'Covered for sick colleague' },
    ],
  },
  {
    id: '2',
    name: 'Olivia Bennett',
    slug: 'olivia-bennett',
    staffId: '12346',
    role: 'Server',
    department: 'Front of House',
    status: 'active',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1974&auto=format&fit=crop',
    dateOfBirth: '1992-06-20',
    gender: 'Female',
    nationality: 'American',
    address: '789 Pine Street',
    city: 'New York',
    postalCode: '10003',
    country: 'USA',
    permissions: ['Manage Orders', 'Customer Service'],
    startDate: '2021-06-20',
    emergencyContactName: 'Michael Bennett',
    emergencyContactNumber: '+****************',
    schedule: [
      { day: 'Monday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Tuesday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
      { day: 'Wednesday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
      { day: 'Thursday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
      { day: 'Friday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
      { day: 'Saturday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
      { day: 'Sunday', startTime: '6:00 PM', endTime: '11:00 PM', status: 'Scheduled' },
    ],
    performance: {
      averageOrderTime: '15 min',
      customerSatisfaction: '95%',
      attendanceRate: '98%',
      ordersCompleted: 890,
      monthlyRating: 4.7,
      teamCollaboration: '92%',
      punctuality: '98%',
      skillLevel: 'Advanced',
      trainingHours: 85,
    },
    documents: [
      { id: 1, name: 'Employment Contract', type: 'PDF', uploadDate: '2021-06-20', size: '2.1 MB' },
      { id: 2, name: 'Alcohol Service License', type: 'PDF', uploadDate: '2023-03-15', size: '1.5 MB' },
      { id: 3, name: 'Customer Service Training', type: 'PDF', uploadDate: '2023-08-10', size: '2.8 MB' },
    ],
    recentActivity: [
      { date: '2024-01-15', action: 'Completed shift', details: 'Front of House - Evening' },
      { date: '2024-01-14', action: 'Customer compliment', details: 'Excellent service feedback' },
      { date: '2024-01-13', action: 'Training session', details: 'Wine pairing workshop' },
    ],
  },
  {
    id: '3',
    name: 'Noah Thompson',
    slug: 'noah-thompson',
    staffId: '12347',
    role: 'Bartender',
    department: 'Bar',
    status: 'active',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop',
    dateOfBirth: '1988-11-10',
    gender: 'Male',
    nationality: 'American',
    address: '321 Maple Drive',
    city: 'New York',
    postalCode: '10004',
    country: 'USA',
    permissions: ['Manage Bar', 'Inventory'],
    startDate: '2019-11-10',
    emergencyContactName: 'Lisa Thompson',
    emergencyContactNumber: '+****************',
    schedule: [
      { day: 'Monday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Tuesday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Wednesday', startTime: '7:00 PM', endTime: '2:00 AM', status: 'Scheduled' },
      { day: 'Thursday', startTime: '7:00 PM', endTime: '2:00 AM', status: 'Scheduled' },
      { day: 'Friday', startTime: '7:00 PM', endTime: '2:00 AM', status: 'Scheduled' },
      { day: 'Saturday', startTime: '7:00 PM', endTime: '2:00 AM', status: 'Scheduled' },
      { day: 'Sunday', startTime: '7:00 PM', endTime: '2:00 AM', status: 'Scheduled' },
    ],
    performance: {
      averageOrderTime: '8 min',
      customerSatisfaction: '97%',
      attendanceRate: '99%',
      ordersCompleted: 2100,
      monthlyRating: 4.8,
      teamCollaboration: '96%',
      punctuality: '99%',
      skillLevel: 'Expert',
      trainingHours: 150,
    },
    documents: [
      { id: 1, name: 'Employment Contract', type: 'PDF', uploadDate: '2019-11-10', size: '2.3 MB' },
      { id: 2, name: 'Bartending License', type: 'PDF', uploadDate: '2023-05-20', size: '1.8 MB' },
      { id: 3, name: 'Mixology Certification', type: 'PDF', uploadDate: '2023-09-15', size: '2.2 MB' },
    ],
    recentActivity: [
      { date: '2024-01-15', action: 'Completed shift', details: 'Bar - Late night shift' },
      { date: '2024-01-14', action: 'New cocktail created', details: 'Signature winter cocktail' },
      { date: '2024-01-13', action: 'Inventory management', details: 'Monthly bar inventory' },
    ],
  },
  {
    id: '4',
    name: 'Ava Harper',
    slug: 'ava-harper',
    staffId: '12348',
    role: 'Hostess',
    department: 'Front of House',
    status: 'inactive',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1974&auto=format&fit=crop',
    dateOfBirth: '1995-01-05',
    gender: 'Female',
    nationality: 'American',
    address: '654 Cedar Lane',
    city: 'New York',
    postalCode: '10005',
    country: 'USA',
    permissions: ['Guest Relations', 'Reservations'],
    startDate: '2022-01-05',
    emergencyContactName: 'James Harper',
    emergencyContactNumber: '+****************',
    schedule: [
      { day: 'Monday', startTime: '5:00 PM', endTime: '10:00 PM', status: 'Scheduled' },
      { day: 'Tuesday', startTime: '5:00 PM', endTime: '10:00 PM', status: 'Scheduled' },
      { day: 'Wednesday', startTime: '5:00 PM', endTime: '10:00 PM', status: 'Scheduled' },
      { day: 'Thursday', startTime: '5:00 PM', endTime: '10:00 PM', status: 'Scheduled' },
      { day: 'Friday', startTime: '5:00 PM', endTime: '10:00 PM', status: 'Scheduled' },
      { day: 'Saturday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Sunday', startTime: 'Off', endTime: 'Off', status: 'Off' },
    ],
    performance: {
      averageOrderTime: '10 min',
      customerSatisfaction: '94%',
      attendanceRate: '96%',
      ordersCompleted: 650,
      monthlyRating: 4.5,
      teamCollaboration: '88%',
      punctuality: '96%',
      skillLevel: 'Intermediate',
      trainingHours: 60,
    },
    documents: [
      { id: 1, name: 'Employment Contract', type: 'PDF', uploadDate: '2022-01-05', size: '2.0 MB' },
      { id: 2, name: 'Customer Service Training', type: 'PDF', uploadDate: '2022-02-15', size: '1.4 MB' },
    ],
    recentActivity: [
      { date: '2024-01-10', action: 'Last shift completed', details: 'Front desk - Evening' },
      { date: '2024-01-05', action: 'Leave of absence', details: 'Personal leave started' },
    ],
  },
  {
    id: '5',
    name: 'Liam Foster',
    slug: 'liam-foster',
    staffId: '12349',
    role: 'Manager',
    department: 'Management',
    status: 'active',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop',
    dateOfBirth: '1980-08-12',
    gender: 'Male',
    nationality: 'American',
    address: '987 Birch Road',
    city: 'New York',
    postalCode: '10006',
    country: 'USA',
    permissions: ['Manage Orders', 'Manage Staff', 'View Reports', 'Financial Access'],
    startDate: '2018-08-12',
    emergencyContactName: 'Emma Foster',
    emergencyContactNumber: '+****************',
    schedule: [
      { day: 'Monday', startTime: '10:00 AM', endTime: '6:00 PM', status: 'Scheduled' },
      { day: 'Tuesday', startTime: '10:00 AM', endTime: '6:00 PM', status: 'Scheduled' },
      { day: 'Wednesday', startTime: '10:00 AM', endTime: '6:00 PM', status: 'Scheduled' },
      { day: 'Thursday', startTime: '10:00 AM', endTime: '6:00 PM', status: 'Scheduled' },
      { day: 'Friday', startTime: '10:00 AM', endTime: '6:00 PM', status: 'Scheduled' },
      { day: 'Saturday', startTime: 'Off', endTime: 'Off', status: 'Off' },
      { day: 'Sunday', startTime: 'Off', endTime: 'Off', status: 'Off' },
    ],
    performance: {
      averageOrderTime: '20 min',
      customerSatisfaction: '96%',
      attendanceRate: '100%',
      ordersCompleted: 450,
      monthlyRating: 4.9,
      teamCollaboration: '98%',
      punctuality: '100%',
      skillLevel: 'Expert',
      trainingHours: 200,
    },
    documents: [
      { id: 1, name: 'Employment Contract', type: 'PDF', uploadDate: '2018-08-12', size: '2.5 MB' },
      { id: 2, name: 'Management Certification', type: 'PDF', uploadDate: '2019-01-15', size: '1.9 MB' },
      { id: 3, name: 'Food Safety Manager License', type: 'PDF', uploadDate: '2023-06-10', size: '1.6 MB' },
      { id: 4, name: 'Leadership Training', type: 'PDF', uploadDate: '2023-11-20', size: '3.2 MB' },
    ],
    recentActivity: [
      { date: '2024-01-15', action: 'Team meeting', details: 'Monthly staff meeting conducted' },
      { date: '2024-01-14', action: 'Performance reviews', details: 'Quarterly staff evaluations' },
      { date: '2024-01-13', action: 'Budget planning', details: 'Q1 budget review and planning' },
    ],
  },
];

interface StaffDetailPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  };
}

export default function StaffDetailPage({ params }: StaffDetailPageProps) {
  const { slugShop, slugBranch, staffSlug } = params;
  const branchWithShop = getBranchWithShop(slugShop, slugBranch);
  const [activeTab, setActiveTab] = useState('overview');

  // Find the staff member by slug
  const staffMember = mockStaffData.find(staff => staff.slug === staffSlug);

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam p-6">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#81766a] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="font-be-vietnam p-6">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#81766a] text-sm">The staff member you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex gap-1 px-6 py-5 font-be-vietnam flex-col">
      {/* Breadcrumb */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`} className="text-[#81766a] text-base font-medium leading-normal">
          Staff
        </Link>
        <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
        <span className="text-[#161412] text-base font-medium leading-normal">Staff Details</span>
      </div>

      <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
        {/* Header with Profile */}
        <div className="flex p-4">
          <div className="flex w-full flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
            <div className="flex gap-4">
              <div
                className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32"
                style={{ backgroundImage: `url("${staffMember.image}")` }}
              ></div>
              <div className="flex flex-col justify-center">
                <p className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em]">{staffMember.name}</p>
                <p className="text-[#81766a] text-base font-normal leading-normal">Staff ID: {staffMember.staffId}</p>
              </div>
            </div>
            <div className="flex items-end">
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}/edit`}>
                <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Staff Member
                </Button>
              </Link>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="pb-3">
          <div className="flex border-b border-[#e3e1dd] px-4 gap-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'overview'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'overview' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Overview</p>
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'schedule'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'schedule' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Schedule</p>
            </button>
            <button
              onClick={() => setActiveTab('performance')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'performance'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'performance' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Performance</p>
            </button>
            <button
              onClick={() => setActiveTab('documents')}
              className={`flex flex-col items-center justify-center border-b-[3px] pb-[13px] pt-4 ${
                activeTab === 'documents'
                  ? 'border-b-[#161412] text-[#161412]'
                  : 'border-b-transparent text-[#81766a]'
              }`}
            >
              <p className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                activeTab === 'documents' ? 'text-[#161412]' : 'text-[#81766a]'
              }`}>Documents</p>
            </button>
          </div>
        </div>

        {/* Personal Details */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Personal Details</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Full Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.name}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Date of Birth</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.dateOfBirth}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Gender</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.gender}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Nationality</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.nationality}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Address</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.address}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">City</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.city}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Postal Code</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.postalCode}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Country</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.country}</p>
          </div>
        </div>

        {/* Role & Permissions */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Role & Permissions</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Role</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.role}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Permissions</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.permissions.join(', ')}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Department</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.department}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Start Date</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{new Date(staffMember.startDate).toLocaleDateString()}</p>
          </div>
        </div>

        {/* Contact Information */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Contact Information</h2>
        <div className="p-4 grid grid-cols-2">
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Email</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.email}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Phone Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.phone}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pr-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Emergency Contact Name</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.emergencyContactName}</p>
          </div>
          <div className="flex flex-col gap-1 border-t border-solid border-t-[#e3e1dd] py-4 pl-2">
            <p className="text-[#81766a] text-sm font-normal leading-normal">Emergency Contact Number</p>
            <p className="text-[#161412] text-sm font-normal leading-normal">{staffMember.emergencyContactNumber}</p>
          </div>
        </div>

        {/* Work Schedule */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Work Schedule</h2>
        <div className="px-4 py-3">
          <div className="flex overflow-hidden rounded-xl border border-[#e3e1dd] bg-white">
            <table className="flex-1">
              <thead>
                <tr className="bg-white">
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Day</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">Start Time</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-[400px] text-sm font-medium leading-normal">End Time</th>
                  <th className="px-4 py-3 text-left text-[#161412] w-60 text-sm font-medium leading-normal">Status</th>
                </tr>
              </thead>
              <tbody>
                {staffMember.schedule.map((scheduleItem, index) => (
                  <tr key={index} className="border-t border-t-[#e3e1dd]">
                    <td className="h-[72px] px-4 py-2 w-[400px] text-[#161412] text-sm font-normal leading-normal">{scheduleItem.day}</td>
                    <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">{scheduleItem.startTime}</td>
                    <td className="h-[72px] px-4 py-2 w-[400px] text-[#81766a] text-sm font-normal leading-normal">{scheduleItem.endTime}</td>
                    <td className="h-[72px] px-4 py-2 w-60 text-sm font-normal leading-normal">
                      <button
                        className={`flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-8 px-4 text-sm font-medium leading-normal w-full ${
                          scheduleItem.status === 'Scheduled'
                            ? 'bg-[#f4f2f1] text-[#161412]'
                            : 'bg-[#f4f2f1] text-[#161412]'
                        }`}
                      >
                        <span className="truncate">{scheduleItem.status}</span>
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Performance Metrics */}
        <h2 className="text-[#161412] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Performance Metrics</h2>
        <div className="flex flex-wrap gap-4 p-4">
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
            <p className="text-[#161412] text-base font-medium leading-normal">Average Order Time</p>
            <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.averageOrderTime}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
            <p className="text-[#161412] text-base font-medium leading-normal">Customer Satisfaction</p>
            <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.customerSatisfaction}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-xl p-6 border border-[#e3e1dd]">
            <p className="text-[#161412] text-base font-medium leading-normal">Attendance Rate</p>
            <p className="text-[#161412] tracking-light text-2xl font-bold leading-tight">{staffMember.performance.attendanceRate}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
