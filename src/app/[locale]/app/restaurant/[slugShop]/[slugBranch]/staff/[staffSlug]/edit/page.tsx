'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, X, Edit } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { getBranchWithShop } from '@/mock/shopData';
import { generateSlug } from '@/lib/utils';
import { toast } from 'sonner';

// Mock staff data (same as in the detail page)
const mockStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    slug: 'ethan-carter',
    role: 'Chef',
    status: 'active',
    schedule: 'Mon-Fri, 9am-5pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced chef with over 10 years in fine dining. Specializes in modern American cuisine with a focus on locally sourced ingredients.',
    startDate: '2020-03-15',
    specialties: ['Modern American', 'Locally Sourced', 'Fine Dining'],
  },
  {
    id: '2',
    name: 'Olivia Bennett',
    slug: 'olivia-bennett',
    role: 'Server',
    status: 'active',
    schedule: 'Tue-Sat, 6pm-11pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?q=80&w=1974&auto=format&fit=crop',
    bio: 'Friendly and professional server with excellent customer service skills. Known for creating memorable dining experiences.',
    startDate: '2021-06-20',
    specialties: ['Customer Service', 'Wine Knowledge', 'Event Service'],
  },
  {
    id: '3',
    name: 'Noah Thompson',
    slug: 'noah-thompson',
    role: 'Bartender',
    status: 'active',
    schedule: 'Wed-Sun, 7pm-2am',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Creative mixologist with a passion for craft cocktails. Specializes in classic cocktails with modern twists.',
    startDate: '2019-11-10',
    specialties: ['Craft Cocktails', 'Mixology', 'Classic Drinks'],
  },
  {
    id: '4',
    name: 'Ava Harper',
    slug: 'ava-harper',
    role: 'Hostess',
    status: 'inactive',
    schedule: 'Mon-Fri, 5pm-10pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?q=80&w=1974&auto=format&fit=crop',
    bio: 'Welcoming hostess who ensures every guest feels valued from the moment they arrive.',
    startDate: '2022-01-05',
    specialties: ['Guest Relations', 'Reservation Management', 'Front of House'],
  },
  {
    id: '5',
    name: 'Liam Foster',
    slug: 'liam-foster',
    role: 'Manager',
    status: 'active',
    schedule: 'Mon-Sun, 10am-6pm',
    email: '<EMAIL>',
    phone: '+****************',
    image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=1974&auto=format&fit=crop',
    bio: 'Experienced restaurant manager with a focus on operational excellence and team development.',
    startDate: '2018-08-12',
    specialties: ['Operations Management', 'Team Leadership', 'Customer Experience'],
  },
];

interface StaffEditPageProps {
  params: {
    slugShop: string;
    slugBranch: string;
    staffSlug: string;
  };
}

export default function StaffEditPage({ params }: StaffEditPageProps) {
  const { slugShop, slugBranch, staffSlug } = params;
  const router = useRouter();
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Find the staff member by slug
  const staffMember = mockStaffData.find(staff => staff.slug === staffSlug);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    email: '',
    phone: '',
    username: '',
    password: '',
    permissions: [] as string[],
  });

  // Available permissions
  const availablePermissions = [
    'Manage Orders',
    'Update Menu',
    'View Reports',
    'Manage Staff'
  ];

  // Initialize form data when staff member is found
  useEffect(() => {
    if (staffMember) {
      setFormData({
        name: staffMember.name,
        role: staffMember.role,
        email: staffMember.email,
        phone: staffMember.phone,
        username: staffMember.slug, // Use slug as username
        password: '', // Don't pre-fill password
        permissions: ['Manage Orders'], // Default permissions
      });
    }
  }, [staffMember]);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const togglePermission = (permission: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...prev.permissions, permission]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.name) {
        toast.error('Name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.email) {
        toast.error('Email is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.role) {
        toast.error('Role is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.username) {
        toast.error('Username is required');
        setIsSubmitting(false);
        return;
      }

      // In a real app, we would call the API to update the staff member
      // const response = await updateStaff({ id: staffMember.id, data: formData }).unwrap();

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Staff member updated successfully');

      // Redirect back to staff detail page
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`);
    } catch (error) {
      toast.error('Failed to update staff member');
      console.error('Error updating staff member:', error);
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  if (!staffMember) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Staff
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Staff Member Not Found</h1>
          <p className="text-[#8a745c] text-sm">The staff member you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Staff Details
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Edit Staff Member</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Update {staffMember.name}'s information at {shop.name} - {branch.name}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="max-w-[512px] space-y-6">
        {/* Full Name */}
        <div className="space-y-2">
          <Label htmlFor="name" className="text-[#181511] text-base font-medium leading-normal text-left">Full Name</Label>
          <Input
            id="name"
            placeholder="Enter full name"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.name}
            onChange={(e) => handleChange('name', e.target.value)}
            required
          />
        </div>

        {/* Role */}
        <div className="space-y-2">
          <Label htmlFor="role" className="text-[#181511] text-base font-medium leading-normal text-left">Role</Label>
          <Select value={formData.role} onValueChange={(value) => handleChange('role', value)}>
            <SelectTrigger className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="chef">Chef</SelectItem>
              <SelectItem value="server">Server</SelectItem>
              <SelectItem value="bartender">Bartender</SelectItem>
              <SelectItem value="hostess">Hostess</SelectItem>
              <SelectItem value="manager">Manager</SelectItem>
              <SelectItem value="cashier">Cashier</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Contact Number */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="text-[#181511] text-base font-medium leading-normal text-left">Contact Number</Label>
          <Input
            id="phone"
            placeholder="Enter contact number"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.phone}
            onChange={(e) => handleChange('phone', e.target.value)}
          />
        </div>

        {/* Email Address */}
        <div className="space-y-2">
          <Label htmlFor="email" className="text-[#181511] text-base font-medium leading-normal text-left">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter email address"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.email}
            onChange={(e) => handleChange('email', e.target.value)}
            required
          />
        </div>

        {/* Username */}
        <div className="space-y-2">
          <Label htmlFor="username" className="text-[#181511] text-base font-medium leading-normal text-left">Username</Label>
          <Input
            id="username"
            placeholder="Enter username"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.username}
            onChange={(e) => handleChange('username', e.target.value)}
            required
          />
        </div>

        {/* Password */}
        <div className="space-y-2">
          <Label htmlFor="password" className="text-[#181511] text-base font-medium leading-normal text-left">Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="Enter new password (leave blank to keep current)"
            className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl text-[#181511] focus:outline-0 focus:ring-0 border border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-14 placeholder:text-[#887663] p-[15px] text-base font-normal leading-normal"
            value={formData.password}
            onChange={(e) => handleChange('password', e.target.value)}
          />
        </div>

        {/* Permissions */}
        <div className="space-y-3">
          <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em]">Permissions</h3>
          <div className="flex gap-3 flex-wrap">
            {availablePermissions.map((permission) => (
              <button
                key={permission}
                type="button"
                onClick={() => togglePermission(permission)}
                className={`flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full pl-4 pr-4 transition-colors ${
                  formData.permissions.includes(permission)
                    ? 'bg-[#e58219] text-white'
                    : 'bg-[#f4f2f0] text-[#181511] hover:bg-[#e5e1dc]'
                }`}
              >
                <p className="text-sm font-medium leading-normal">{permission}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/staff/${staffSlug}`}>
            <Button type="button" variant="outline" className="border-[#e2dcd4] text-[#181510]">
              Cancel
            </Button>
          </Link>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] hover:bg-[#d4741a] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
          >
            <span className="truncate">
              {isSubmitting ? 'Saving...' : 'Save Changes'}
            </span>
          </Button>
        </div>
      </form>
    </div>
  );
}
