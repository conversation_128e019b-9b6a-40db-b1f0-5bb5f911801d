'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useGetReviewByIdQuery, useRespondToReviewMutation, useUpdateReviewStatusMutation } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { StarRating } from '@/components/ui/star-rating';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { format, parseISO } from 'date-fns';
import { ArrowLeft, MessageSquare, Flag, Archive, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import mockReviews from '@/mock/reviewData';

interface ReviewDetailPageProps {
  params: {
    id: string;
  };
}

export default function ReviewDetailPage({ params }: ReviewDetailPageProps) {
  // Use state to store the ID to avoid the Next.js warning about synchronous params access
  const [reviewId, setReviewId] = useState<string>('');
  const router = useRouter();

  // Set the review ID from params after component mounts
  useEffect(() => {
    if (params && params.id) {
      setReviewId(params.id);
    }
  }, [params]);

  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // State for UI
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [responseText, setResponseText] = useState('');

  // Use mock data for now, but in a real app we would use the API
  // const { data: review, isLoading, refetch } = useGetReviewByIdQuery({ merchantId, reviewId });
  // const [respondToReview, { isLoading: isResponding }] = useRespondToReviewMutation();
  // const [updateReviewStatus, { isLoading: isUpdating }] = useUpdateReviewStatusMutation();
  const [review, setReview] = useState<typeof mockReviews[0] | undefined>(undefined);
  const isLoading = !reviewId;
  const isResponding = false;
  const isUpdating = false;

  // Update the review when reviewId changes
  useEffect(() => {
    if (reviewId) {
      setReview(mockReviews.find(r => r.id === reviewId));
    }
  }, [reviewId]);

  // Handle responding to the review
  const handleRespond = () => {
    setResponseText(review?.response || '');
    setIsRespondDialogOpen(true);
  };

  // Submit response to the review
  const submitResponse = async () => {
    if (!responseText.trim() || !reviewId) return;

    try {
      // In a real app, we would call the API
      // await respondToReview({
      //   merchantId,
      //   reviewId,
      //   response: responseText.trim()
      // }).unwrap();

      // Update the mock data
      if (review) {
        const updatedReview = { ...review, response: responseText.trim() };
        setReview(updatedReview);

        // Also update in the mock reviews list
        const reviewIndex = mockReviews.findIndex(r => r.id === reviewId);
        if (reviewIndex !== -1) {
          mockReviews[reviewIndex] = updatedReview;
        }
      }

      toast.success('Response submitted successfully');
      setIsRespondDialogOpen(false);
    } catch (error) {
      toast.error('Failed to submit response');
      console.error('Error responding to review:', error);
    }
  };

  // Handle updating review status
  const handleUpdateStatus = async (status: string) => {
    if (!reviewId) return;

    try {
      // In a real app, we would call the API
      // await updateReviewStatus({ merchantId, reviewId, status }).unwrap();

      // Update the mock data
      if (review) {
        const updatedReview = { ...review, status };
        setReview(updatedReview);

        // Also update in the mock reviews list
        const reviewIndex = mockReviews.findIndex(r => r.id === reviewId);
        if (reviewIndex !== -1) {
          mockReviews[reviewIndex] = updatedReview;
        }
      }

      toast.success(`Review ${status} successfully`);
    } catch (error) {
      toast.error(`Failed to update review status`);
      console.error('Error updating review status:', error);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Published</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'flagged':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Flagged</Badge>;
      case 'archived':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Archived</Badge>;
      default:
        return null;
    }
  };

  if (isLoading) {
    return <div className="p-6 text-center">Loading review details...</div>;
  }

  if (!review) {
    return (
      <div className="p-6 text-center">
        <p className="text-[#8a745c] mb-4">Review not found</p>
        <Link href="/app/restaurant/reviews">
          <Button variant="outline" className="border-[#e5e1dc]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Reviews
          </Button>
        </Link>
      </div>
    );
  }

  // Format the date
  const formattedDate = review.date ? format(parseISO(review.date), 'MMMM d, yyyy') : '';

  return (
    <div className="p-6 font-be-vietnam">
      {/* Header */}
      <div className="flex flex-wrap justify-between items-center gap-3 mb-6">
        <div className="flex items-center gap-4">
          <Link href="/app/restaurant/reviews">
            <Button variant="outline" className="border-[#e5e1dc]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reviews
            </Button>
          </Link>
          <h1 className="text-[#181510] text-2xl font-bold leading-tight">Review Details</h1>
        </div>
        <div className="flex items-center gap-2">
          {getStatusBadge(review.status)}
        </div>
      </div>

      {/* Review Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-3">
                  {review.reviewer.avatar ? (
                    <div
                      className="w-12 h-12 rounded-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${review.reviewer.avatar})` }}
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-[#e5e1dc] flex items-center justify-center text-[#8a745c] font-medium text-lg">
                      {review.reviewer.name.charAt(0)}
                    </div>
                  )}
                  <div>
                    <div className="font-medium text-[#181510] text-lg">{review.reviewer.name}</div>
                    <div className="text-sm text-[#8a745c]">{formattedDate}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <StarRating rating={review.rating} size="md" />
                  <span className="text-lg font-medium text-[#181510]">{review.rating}</span>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <p className="text-[#181510] text-base my-4">{review.content}</p>

              {review.reviewedItems && review.reviewedItems.length > 0 && (
                <div className="mt-6 mb-4">
                  <div className="text-sm font-medium text-[#8a745c] mb-2">Reviewed Items:</div>
                  <div className="flex flex-wrap gap-2">
                    {review.reviewedItems.map((item) => (
                      <Badge
                        key={item.id}
                        variant="outline"
                        className="text-sm bg-[#f1edea] border-[#e5e1dc] text-[#8a745c]"
                      >
                        {item.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {review.response && (
                <div className="mt-6 bg-[#f1edea] p-4 rounded-md">
                  <div className="text-sm font-medium text-[#8a745c] mb-2">Your Response:</div>
                  <p className="text-[#181510] text-base">{review.response}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="md:col-span-1">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc] overflow-hidden">
            <CardHeader>
              <h2 className="text-[#181510] text-lg font-medium">Actions</h2>
            </CardHeader>

            <CardContent className="flex flex-col gap-3">
              <Button
                variant="outline"
                className="w-full justify-start border-[#e5e1dc]"
                onClick={handleRespond}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {review.response ? 'Edit Response' : 'Respond to Review'}
              </Button>

              {review.status !== 'flagged' && (
                <Button
                  variant="outline"
                  className="w-full justify-start border-[#e5e1dc]"
                  onClick={() => handleUpdateStatus('flagged')}
                  disabled={isUpdating}
                >
                  <Flag className="h-4 w-4 mr-2" />
                  Flag Review
                </Button>
              )}

              {review.status !== 'archived' && (
                <Button
                  variant="outline"
                  className="w-full justify-start border-[#e5e1dc]"
                  onClick={() => handleUpdateStatus('archived')}
                  disabled={isUpdating}
                >
                  <Archive className="h-4 w-4 mr-2" />
                  Archive Review
                </Button>
              )}

              {review.status !== 'published' && (
                <Button
                  variant="outline"
                  className="w-full justify-start border-[#e5e1dc]"
                  onClick={() => handleUpdateStatus('published')}
                  disabled={isUpdating}
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Publish Review
                </Button>
              )}
            </CardContent>
          </Card>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc] overflow-hidden mt-4">
            <CardHeader>
              <h2 className="text-[#181510] text-lg font-medium">Reviewer Information</h2>
            </CardHeader>

            <CardContent>
              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-[#8a745c]">Name</div>
                  <div className="text-[#181510]">{review.reviewer.name}</div>
                </div>

                {review.reviewer.email && (
                  <div>
                    <div className="text-sm font-medium text-[#8a745c]">Email</div>
                    <div className="text-[#181510]">{review.reviewer.email}</div>
                  </div>
                )}

                <div>
                  <div className="text-sm font-medium text-[#8a745c]">Date</div>
                  <div className="text-[#181510]">{formattedDate}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Respond Dialog */}
      <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e5e1dc] w-[95%] max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle className="text-[#181510]">
              {review.response ? 'Edit Response' : 'Respond to Review'}
            </DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <div className="space-y-2">
              <label htmlFor="response" className="text-sm font-medium text-[#8a745c]">
                Your Response
              </label>
              <Textarea
                id="response"
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                placeholder="Type your response here..."
                className="bg-white border-[#e5e1dc] min-h-[150px]"
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRespondDialogOpen(false)}
              className="border-[#e5e1dc]"
            >
              Cancel
            </Button>
            <Button
              onClick={submitResponse}
              disabled={!responseText.trim() || isResponding}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
            >
              {isResponding ? 'Submitting...' : 'Submit Response'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
