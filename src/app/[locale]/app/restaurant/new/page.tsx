'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Save } from 'lucide-react';
import { toast } from 'sonner';

export default function NewShopPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    slug: '',
    logo: '',
    coverImage: '',
    address: '',
    phoneNumber: '',
    email: '',
    website: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
    },
    cuisineType: '',
    priceRange: 'medium',
  });

  // Handle form field changes
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle social media changes
  const handleSocialMediaChange = (platform: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      socialMedia: {
        ...prev.socialMedia,
        [platform]: value
      }
    }));
  };

  // Generate slug from name
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
  };

  // Handle name change and auto-generate slug
  const handleNameChange = (name: string) => {
    handleChange('name', name);
    if (!formData.slug || formData.slug === generateSlug(formData.name)) {
      handleChange('slug', generateSlug(name));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!formData.name) {
        toast.error('Restaurant name is required');
        setIsSubmitting(false);
        return;
      }

      if (!formData.slug) {
        toast.error('Slug is required');
        setIsSubmitting(false);
        return;
      }

      // In a real app, we would call the API
      // const response = await createShop(formData).unwrap();
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Restaurant created successfully');
      
      // Redirect to the new shop page
      router.push(`/app/restaurant/${formData.slug}`);
    } catch (error) {
      toast.error('Failed to create restaurant');
      console.error('Error creating restaurant:', error);
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href="/app/restaurant">
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Restaurants
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Create New Restaurant</h1>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">
            Set up a new restaurant in your account
          </p>
        </div>
        <div className="flex items-end">
          <Button 
            className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            <Save className="mr-2 h-4 w-4" />
            {isSubmitting ? 'Creating...' : 'Create Restaurant'}
          </Button>
        </div>
      </div>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>General information about your restaurant</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Restaurant Name*</Label>
                <Input 
                  id="name" 
                  value={formData.name} 
                  onChange={(e) => handleNameChange(e.target.value)}
                  placeholder="e.g. Bella Italia"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug*</Label>
                <Input 
                  id="slug" 
                  value={formData.slug} 
                  onChange={(e) => handleChange('slug', e.target.value)}
                  placeholder="e.g. bella-italia"
                  required
                />
                <p className="text-xs text-[#8a745c]">
                  This will be used in your restaurant's URL: /restaurant/{formData.slug || 'your-slug'}
                </p>
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  value={formData.description} 
                  onChange={(e) => handleChange('description', e.target.value)}
                  placeholder="Describe your restaurant"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="cuisineType">Cuisine Type</Label>
                <Input 
                  id="cuisineType" 
                  value={formData.cuisineType} 
                  onChange={(e) => handleChange('cuisineType', e.target.value)}
                  placeholder="e.g. Italian, Mexican, etc."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priceRange">Price Range</Label>
                <select 
                  id="priceRange"
                  className="w-full p-2 rounded-md border border-[#e5e1dc] bg-[#fbfaf9]"
                  value={formData.priceRange}
                  onChange={(e) => handleChange('priceRange', e.target.value)}
                >
                  <option value="budget">Budget ($)</option>
                  <option value="medium">Medium ($$)</option>
                  <option value="premium">Premium ($$$)</option>
                  <option value="luxury">Luxury ($$$$)</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>How customers can reach your restaurant</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="address">Address</Label>
                <Textarea 
                  id="address" 
                  value={formData.address} 
                  onChange={(e) => handleChange('address', e.target.value)}
                  placeholder="Full address"
                  rows={2}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input 
                  id="phoneNumber" 
                  value={formData.phoneNumber} 
                  onChange={(e) => handleChange('phoneNumber', e.target.value)}
                  placeholder="e.g. +****************"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  type="email"
                  value={formData.email} 
                  onChange={(e) => handleChange('email', e.target.value)}
                  placeholder="e.g. <EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input 
                  id="website" 
                  value={formData.website} 
                  onChange={(e) => handleChange('website', e.target.value)}
                  placeholder="e.g. https://www.restaurant.com"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Social Media</CardTitle>
            <CardDescription>Connect your restaurant's social media accounts</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="facebook">Facebook</Label>
                <Input 
                  id="facebook" 
                  value={formData.socialMedia.facebook} 
                  onChange={(e) => handleSocialMediaChange('facebook', e.target.value)}
                  placeholder="e.g. https://facebook.com/yourrestaurant"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="instagram">Instagram</Label>
                <Input 
                  id="instagram" 
                  value={formData.socialMedia.instagram} 
                  onChange={(e) => handleSocialMediaChange('instagram', e.target.value)}
                  placeholder="e.g. https://instagram.com/yourrestaurant"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="twitter">Twitter</Label>
                <Input 
                  id="twitter" 
                  value={formData.socialMedia.twitter} 
                  onChange={(e) => handleSocialMediaChange('twitter', e.target.value)}
                  placeholder="e.g. https://twitter.com/yourrestaurant"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Images</CardTitle>
            <CardDescription>Upload images for your restaurant</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="logo">Logo URL</Label>
                <Input 
                  id="logo" 
                  value={formData.logo} 
                  onChange={(e) => handleChange('logo', e.target.value)}
                  placeholder="e.g. https://example.com/logo.png"
                />
                <p className="text-xs text-[#8a745c]">
                  Enter a URL for your restaurant logo
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="coverImage">Cover Image URL</Label>
                <Input 
                  id="coverImage" 
                  value={formData.coverImage} 
                  onChange={(e) => handleChange('coverImage', e.target.value)}
                  placeholder="e.g. https://example.com/cover.jpg"
                />
                <p className="text-xs text-[#8a745c]">
                  Enter a URL for your restaurant cover image
                </p>
              </div>
            </div>
            <p className="text-sm text-[#8a745c] italic">
              Note: In a production app, you would be able to upload images directly.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
