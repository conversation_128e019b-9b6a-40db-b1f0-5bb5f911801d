'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useGetTablesQuery, useCreateTableMutation, useUpdateTableMutation, useDeleteTableMutation } from '@/lib/redux/api/endpoints/restaurantApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Trash2,
  Save,
  ArrowLeft,
  Move,
  Square,
  Circle,
  Settings,
  Edit,
  LayoutGrid
} from 'lucide-react';

// Mock data for tables (will be replaced with real data from API)
const mockTables = [
  {
    id: '1',
    number: 1,
    capacity: 4,
    status: 'available',
    location: 'dining',
    shape: 'square',
    positionX: 100,
    positionY: 100,
    width: 80,
    height: 80,
  },
  {
    id: '2',
    number: 2,
    capacity: 2,
    status: 'available',
    location: 'dining',
    shape: 'circle',
    positionX: 250,
    positionY: 100,
    width: 60,
    height: 60,
  },
  {
    id: '3',
    number: 3,
    capacity: 6,
    status: 'available',
    location: 'dining',
    shape: 'rectangle',
    positionX: 100,
    positionY: 250,
    width: 120,
    height: 80,
  },
  {
    id: '4',
    number: 4,
    capacity: 2,
    status: 'available',
    location: 'outdoor',
    shape: 'circle',
    positionX: 400,
    positionY: 150,
    width: 60,
    height: 60,
  },
  {
    id: '5',
    number: 5,
    capacity: 4,
    status: 'available',
    location: 'outdoor',
    shape: 'square',
    positionX: 400,
    positionY: 300,
    width: 80,
    height: 80,
  },
];

// Define default floors
const defaultFloors = [
  { id: 'floor-1', name: 'Ground Floor', order: 1 },
  { id: 'floor-2', name: 'First Floor', order: 2 },
];

// Define default areas
const defaultAreas = [
  { id: 'dining', name: 'Dining Area', floorId: 'floor-1' },
  { id: 'outdoor', name: 'Outdoor Patio', floorId: 'floor-1' },
  { id: 'bar', name: 'Bar Area', floorId: 'floor-1' },
  { id: 'private', name: 'Private Room', floorId: 'floor-1' },
  { id: 'vip', name: 'VIP Lounge', floorId: 'floor-2' },
  { id: 'event', name: 'Event Space', floorId: 'floor-2' },
];

// Define table shapes
const tableShapes = [
  { id: 'square', name: 'Square', icon: <Square className="h-4 w-4" /> },
  { id: 'circle', name: 'Circle', icon: <Circle className="h-4 w-4" /> },
  { id: 'rectangle', name: 'Rectangle', icon: <Square className="h-4 w-4 rotate-90" /> },
];

interface Floor {
  id: string;
  name: string;
  order: number;
}

interface Area {
  id: string;
  name: string;
  floorId: string;
}

interface Table {
  id: string;
  number: number;
  capacity: number;
  status: string;
  location: string; // area id
  shape: string;
  positionX: number;
  positionY: number;
  width: number;
  height: number;
}

export default function LayoutEditorPage() {
  const router = useRouter();

  // In a real app, we would use the merchantId from context or auth
  const merchantId = "current-merchant-id";

  // State for tables and UI
  const [tables, setTables] = useState<Table[]>(mockTables);
  const [floors, setFloors] = useState<Floor[]>(defaultFloors);
  const [areas, setAreas] = useState<Area[]>(defaultAreas);
  const [selectedFloor, setSelectedFloor] = useState('floor-1');
  const [selectedArea, setSelectedArea] = useState('dining');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedAreaForEdit, setSelectedAreaForEdit] = useState<Area | null>(null);
  const [selectedFloorForEdit, setSelectedFloorForEdit] = useState<Floor | null>(null);
  const [isAddTableOpen, setIsAddTableOpen] = useState(false);
  const [isEditTableOpen, setIsEditTableOpen] = useState(false);
  const [isManageAreasOpen, setIsManageAreasOpen] = useState(false);
  const [isAddAreaOpen, setIsAddAreaOpen] = useState(false);
  const [isEditAreaOpen, setIsEditAreaOpen] = useState(false);
  const [isDeleteAreaOpen, setIsDeleteAreaOpen] = useState(false);
  const [isManageFloorsOpen, setIsManageFloorsOpen] = useState(false);
  const [isAddFloorOpen, setIsAddFloorOpen] = useState(false);
  const [isEditFloorOpen, setIsEditFloorOpen] = useState(false);
  const [isDeleteFloorOpen, setIsDeleteFloorOpen] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement>(null);

  // Form state for adding/editing tables
  const [tableForm, setTableForm] = useState({
    number: 1,
    capacity: 2,
    shape: 'square',
    width: 80,
    height: 80,
  });

  // Form state for adding/editing areas
  const [areaForm, setAreaForm] = useState({
    name: '',
    floorId: selectedFloor,
  });

  // Form state for adding/editing floors
  const [floorForm, setFloorForm] = useState({
    name: '',
    order: 1,
  });

  // Filter areas by selected floor
  const filteredAreas = areas.filter(area => area.floorId === selectedFloor);

  // Filter tables by selected area
  const filteredTables = tables.filter(table => table.location === selectedArea);

  // Handle table selection
  const handleTableClick = (table: Table, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedTable(table);
  };

  // Handle canvas click (deselect table)
  const handleCanvasClick = () => {
    setSelectedTable(null);
  };

  // Handle table drag start (works for both mouse and touch)
  const handleDragStart = (e: React.MouseEvent | React.TouchEvent, table: Table) => {
    e.stopPropagation();
    setIsDragging(true);
    setSelectedTable(table);

    // Calculate offset from pointer position to table position
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    setDragOffset({
      x: clientX - rect.left,
      y: clientY - rect.top,
    });
  };

  // Handle pointer move for dragging (works for both mouse and touch)
  const handlePointerMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !selectedTable || !canvasRef.current) return;

    // Get canvas bounds
    const canvasRect = canvasRef.current.getBoundingClientRect();

    // Get client coordinates (works for both mouse and touch)
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    // Calculate new position
    const newX = clientX - canvasRect.left - dragOffset.x;
    const newY = clientY - canvasRect.top - dragOffset.y;

    // Ensure table stays within canvas bounds
    const boundedX = Math.max(0, Math.min(newX, canvasRect.width - (selectedTable.width || 80)));
    const boundedY = Math.max(0, Math.min(newY, canvasRect.height - (selectedTable.height || 80)));

    // Update table position
    setTables(tables.map(table =>
      table.id === selectedTable.id
        ? { ...table, positionX: boundedX, positionY: boundedY }
        : table
    ));
  };

  // Handle pointer up to end dragging (works for both mouse and touch)
  const handlePointerUp = () => {
    if (isDragging) {
      setIsDragging(false);
      // In a real app, we would save the updated position to the API
      toast.success('Table position updated');
    }
  };

  // Handle adding a new table
  const handleAddTable = () => {
    const newTable: Table = {
      id: `new-${Date.now()}`,
      number: Math.max(0, ...tables.map(t => t.number)) + 1,
      capacity: tableForm.capacity,
      status: 'available',
      location: selectedArea,
      shape: tableForm.shape,
      positionX: 100,
      positionY: 100,
      width: tableForm.width,
      height: tableForm.height,
    };

    setTables([...tables, newTable]);
    setIsAddTableOpen(false);
    setTableForm({
      number: newTable.number + 1,
      capacity: 2,
      shape: 'square',
      width: 80,
      height: 80,
    });

    toast.success('Table added successfully');
  };

  // Handle editing a table
  const handleEditTable = () => {
    if (!selectedTable) return;

    setTables(tables.map(table =>
      table.id === selectedTable.id
        ? {
            ...table,
            capacity: tableForm.capacity,
            shape: tableForm.shape,
            width: tableForm.width,
            height: tableForm.height,
          }
        : table
    ));

    setIsEditTableOpen(false);
    toast.success('Table updated successfully');
  };

  // Handle deleting a table
  const handleDeleteTable = () => {
    if (!selectedTable) return;

    setTables(tables.filter(table => table.id !== selectedTable.id));
    setSelectedTable(null);
    toast.success('Table deleted successfully');
  };

  // Open edit dialog
  const openEditDialog = () => {
    if (!selectedTable) return;

    setTableForm({
      number: selectedTable.number,
      capacity: selectedTable.capacity,
      shape: selectedTable.shape,
      width: selectedTable.width,
      height: selectedTable.height,
    });

    setIsEditTableOpen(true);
  };

  // Save layout
  const saveLayout = () => {
    // In a real app, we would save all tables and areas to the API
    toast.success('Layout saved successfully');
  };

  // Handle adding a new area
  const handleAddArea = () => {
    if (!areaForm.name.trim()) {
      toast.error('Area name cannot be empty');
      return;
    }

    // Generate a unique ID for the new area
    const newAreaId = `area-${Date.now()}`;

    // Create the new area
    const newArea: Area = {
      id: newAreaId,
      name: areaForm.name.trim(),
      floorId: areaForm.floorId || selectedFloor,
    };

    // Add the new area to the areas array
    setAreas([...areas, newArea]);

    // Close the dialog and reset the form
    setIsAddAreaOpen(false);
    setAreaForm({ name: '', floorId: selectedFloor });

    // Select the new area
    setSelectedArea(newAreaId);

    // Make sure the correct floor is selected
    if (newArea.floorId !== selectedFloor) {
      setSelectedFloor(newArea.floorId);
    }

    toast.success('Area added successfully');
  };

  // Handle editing an area
  const handleEditArea = () => {
    if (!selectedAreaForEdit) return;

    if (!areaForm.name.trim()) {
      toast.error('Area name cannot be empty');
      return;
    }

    const wasOnSameFloor = selectedAreaForEdit.floorId === areaForm.floorId;

    // Update the area
    setAreas(areas.map(area =>
      area.id === selectedAreaForEdit.id
        ? { ...area, name: areaForm.name.trim(), floorId: areaForm.floorId }
        : area
    ));

    // Close the dialog and reset the form
    setIsEditAreaOpen(false);
    setSelectedAreaForEdit(null);
    setAreaForm({ name: '', floorId: selectedFloor });

    // If the area was moved to a different floor and it was the selected area,
    // update the selected floor and area
    if (!wasOnSameFloor && selectedArea === selectedAreaForEdit.id) {
      setSelectedFloor(areaForm.floorId);
      setSelectedArea(selectedAreaForEdit.id);
    }

    toast.success('Area updated successfully');
  };

  // Handle deleting an area
  const handleDeleteArea = () => {
    if (!selectedAreaForEdit) return;

    // Check if there are tables in this area
    const tablesInArea = tables.filter(table => table.location === selectedAreaForEdit.id);

    if (tablesInArea.length > 0) {
      // Ask for confirmation if there are tables in this area
      if (!confirm(`This area contains ${tablesInArea.length} table(s). Deleting this area will also delete all tables in it. Are you sure you want to continue?`)) {
        return;
      }

      // Remove tables in this area
      setTables(tables.filter(table => table.location !== selectedAreaForEdit.id));
    }

    // Remove the area
    setAreas(areas.filter(area => area.id !== selectedAreaForEdit.id));

    // If the deleted area was selected, select the first available area
    if (selectedArea === selectedAreaForEdit.id) {
      const firstArea = areas.find(area => area.id !== selectedAreaForEdit.id);
      if (firstArea) {
        setSelectedArea(firstArea.id);
      }
    }

    // Close the dialog and reset the form
    setIsDeleteAreaOpen(false);
    setSelectedAreaForEdit(null);

    toast.success('Area deleted successfully');
  };

  // Open edit area dialog
  const openEditAreaDialog = (area: Area) => {
    setSelectedAreaForEdit(area);
    setAreaForm({ name: area.name });
    setIsEditAreaOpen(true);
  };

  // Open delete area dialog
  const openDeleteAreaDialog = (area: Area) => {
    setSelectedAreaForEdit(area);
    setIsDeleteAreaOpen(true);
  };

  // Handle adding a new floor
  const handleAddFloor = () => {
    if (!floorForm.name.trim()) {
      toast.error('Floor name cannot be empty');
      return;
    }

    // Generate a unique ID for the new floor
    const newFloorId = `floor-${Date.now()}`;

    // Create the new floor
    const newFloor: Floor = {
      id: newFloorId,
      name: floorForm.name.trim(),
      order: floorForm.order,
    };

    // Add the new floor to the floors array
    setFloors([...floors, newFloor].sort((a, b) => a.order - b.order));

    // Close the dialog and reset the form
    setIsAddFloorOpen(false);
    setFloorForm({ name: '', order: Math.max(...floors.map(f => f.order), 0) + 1 });

    // Select the new floor
    setSelectedFloor(newFloorId);

    toast.success('Floor added successfully');
  };

  // Handle editing a floor
  const handleEditFloor = () => {
    if (!selectedFloorForEdit) return;

    if (!floorForm.name.trim()) {
      toast.error('Floor name cannot be empty');
      return;
    }

    // Update the floor
    setFloors(
      floors.map(floor =>
        floor.id === selectedFloorForEdit.id
          ? { ...floor, name: floorForm.name.trim(), order: floorForm.order }
          : floor
      ).sort((a, b) => a.order - b.order)
    );

    // Close the dialog and reset the form
    setIsEditFloorOpen(false);
    setSelectedFloorForEdit(null);
    setFloorForm({ name: '', order: 1 });

    toast.success('Floor updated successfully');
  };

  // Handle deleting a floor
  const handleDeleteFloor = () => {
    if (!selectedFloorForEdit) return;

    // Check if there are areas in this floor
    const areasInFloor = areas.filter(area => area.floorId === selectedFloorForEdit.id);

    if (areasInFloor.length > 0) {
      // Ask for confirmation if there are areas in this floor
      if (!confirm(`This floor contains ${areasInFloor.length} area(s). Deleting this floor will also delete all areas and tables in it. Are you sure you want to continue?`)) {
        return;
      }

      // Get all tables in areas of this floor
      const areaIds = areasInFloor.map(area => area.id);

      // Remove tables in these areas
      setTables(tables.filter(table => !areaIds.includes(table.location)));

      // Remove areas in this floor
      setAreas(areas.filter(area => area.floorId !== selectedFloorForEdit.id));
    }

    // Remove the floor
    setFloors(floors.filter(floor => floor.id !== selectedFloorForEdit.id));

    // If the deleted floor was selected, select the first available floor
    if (selectedFloor === selectedFloorForEdit.id) {
      const firstFloor = floors.find(floor => floor.id !== selectedFloorForEdit.id);
      if (firstFloor) {
        setSelectedFloor(firstFloor.id);

        // Also select the first area in the new selected floor
        const firstArea = areas.find(area => area.floorId === firstFloor.id);
        if (firstArea) {
          setSelectedArea(firstArea.id);
        }
      }
    }

    // Close the dialog and reset the form
    setIsDeleteFloorOpen(false);
    setSelectedFloorForEdit(null);

    toast.success('Floor deleted successfully');
  };

  // Open edit floor dialog
  const openEditFloorDialog = (floor: Floor) => {
    setSelectedFloorForEdit(floor);
    setFloorForm({ name: floor.name, order: floor.order });
    setIsEditFloorOpen(true);
  };

  // Open delete floor dialog
  const openDeleteFloorDialog = (floor: Floor) => {
    setSelectedFloorForEdit(floor);
    setIsDeleteFloorOpen(true);
  };

  // Handle floor change
  const handleFloorChange = (floorId: string) => {
    setSelectedFloor(floorId);

    // Select the first area in the new floor
    const firstArea = areas.find(area => area.floorId === floorId);
    if (firstArea) {
      setSelectedArea(firstArea.id);
    }
  };

  // Get table style based on shape
  const getTableStyle = (table: Table) => {
    const baseStyle = {
      position: 'absolute' as const,
      left: `${table.positionX}px`,
      top: `${table.positionY}px`,
      width: `${table.width}px`,
      height: `${table.height}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'move',
      userSelect: 'none' as const,
      backgroundColor: selectedTable?.id === table.id ? '#e5ccb2' : '#f1edea',
      color: '#181510',
      fontWeight: 'bold',
      border: selectedTable?.id === table.id ? '2px solid #8a745c' : '1px solid #e2dcd4',
    };

    if (table.shape === 'circle') {
      return {
        ...baseStyle,
        borderRadius: '50%',
      };
    } else if (table.shape === 'rectangle') {
      return {
        ...baseStyle,
        borderRadius: '4px',
      };
    } else {
      return {
        ...baseStyle,
        borderRadius: '4px',
      };
    }
  };

  return (
    <div className="p-3 sm:p-6 font-be-vietnam">
      {/* Header - Responsive for mobile */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-[#181510]">Table Layout Editor</h1>
          <p className="text-[#8a745c] text-sm">Design your restaurant floor plan</p>
        </div>
        <div className="flex flex-wrap gap-2 sm:gap-3 w-full sm:w-auto">
          <Link href="/app/restaurant/tables" className="w-1/2 sm:w-auto">
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510] w-full sm:w-auto">
              <ArrowLeft className="h-4 w-4 mr-2 hidden sm:inline" />
              Back
            </Button>
          </Link>
          <Button
            className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-1/2 sm:w-auto"
            onClick={saveLayout}
          >
            <Save className="h-4 w-4 mr-2 hidden sm:inline" />
            Save Layout
          </Button>
        </div>
      </div>

      {/* Floor selector */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-base sm:text-lg font-medium text-[#181510]">Floor</h2>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setIsManageFloorsOpen(true)}
          >
            <Settings className="h-4 w-4 text-[#8a745c]" />
            <span className="sr-only">Manage Floors</span>
          </Button>
        </div>

        <Select value={selectedFloor} onValueChange={handleFloorChange}>
          <SelectTrigger className="w-full bg-[#fbfaf9] border-[#e2dcd4] h-9 sm:h-10">
            <SelectValue placeholder="Select a floor" />
          </SelectTrigger>
          <SelectContent>
            {floors.map(floor => (
              <SelectItem key={floor.id} value={floor.id}>
                {floor.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Main content - Responsive grid layout */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6">
        {/* Sidebar - Areas and Selected Table */}
        <div className="lg:col-span-1 order-2 lg:order-1">
          {/* Areas section */}
          <div className="bg-[#f1edea] p-3 sm:p-4 rounded-lg mb-4 sm:mb-6">
            <div className="flex justify-between items-center mb-3 sm:mb-4">
              <h2 className="text-base sm:text-lg font-medium text-[#181510]">Areas</h2>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => setIsManageAreasOpen(true)}
              >
                <Settings className="h-4 w-4 text-[#8a745c]" />
                <span className="sr-only">Manage Areas</span>
              </Button>
            </div>

            <div className="mb-3 sm:mb-4">
              <Select
                value={selectedArea}
                onValueChange={setSelectedArea}
                disabled={filteredAreas.length === 0}
              >
                <SelectTrigger className="w-full bg-[#fbfaf9] border-[#e2dcd4] h-9 sm:h-10">
                  <SelectValue placeholder={filteredAreas.length === 0 ? "No areas on this floor" : "Select an area"} />
                </SelectTrigger>
                <SelectContent>
                  {filteredAreas.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                className="flex-1 bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] text-sm"
                onClick={() => setIsAddTableOpen(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Table
              </Button>

              <Button
                className="flex-1 bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border border-[#e2dcd4] text-sm"
                onClick={() => {
                  setAreaForm({ name: '', floorId: selectedFloor });
                  setIsAddAreaOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Area
              </Button>
            </div>
          </div>

          {/* Selected Table section - Only shown when a table is selected */}
          {selectedTable && (
            <div className="bg-[#f1edea] p-3 sm:p-4 rounded-lg">
              <h2 className="text-base sm:text-lg font-medium mb-3 sm:mb-4 text-[#181510]">Selected Table</h2>
              <div className="space-y-2 sm:space-y-3">
                <div>
                  <p className="text-xs sm:text-sm text-[#8a745c]">Table Number</p>
                  <p className="font-medium">{selectedTable.number}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-[#8a745c]">Capacity</p>
                  <p className="font-medium">{selectedTable.capacity} people</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-[#8a745c]">Shape</p>
                  <p className="font-medium capitalize">{selectedTable.shape}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-[#8a745c]">Location</p>
                  <p className="font-medium capitalize">{selectedTable.location}</p>
                </div>

                <div className="flex space-x-2 pt-2">
                  <Button
                    className="flex-1 bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border border-[#e2dcd4] text-xs sm:text-sm"
                    onClick={openEditDialog}
                  >
                    Edit
                  </Button>
                  <Button
                    className="flex-1 bg-[#f1edea] text-[#181510] hover:bg-red-50 hover:text-red-600 hover:border-red-200 border border-[#e2dcd4] text-xs sm:text-sm"
                    onClick={handleDeleteTable}
                  >
                    <Trash2 className="h-4 w-4 mr-1 sm:mr-2" />
                    Delete
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Canvas - Responsive height and touch support */}
        <div
          className="lg:col-span-3 bg-[#fbfaf9] border border-[#e2dcd4] rounded-lg p-3 sm:p-4 relative h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] overflow-hidden order-1 lg:order-2"
          ref={canvasRef}
          onClick={handleCanvasClick}
          onMouseMove={handlePointerMove}
          onTouchMove={handlePointerMove}
          onMouseUp={handlePointerUp}
          onTouchEnd={handlePointerUp}
          onMouseLeave={handlePointerUp}
          onTouchCancel={handlePointerUp}
        >
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h2 className="text-base sm:text-lg font-medium text-[#181510]">
              {areas.find(area => area.id === selectedArea)?.name || 'Layout'}
            </h2>
            <div className="text-xs sm:text-sm text-[#8a745c] bg-[#f1edea] px-2 py-1 rounded-md">
              {tables.filter(table => table.location === selectedArea).length} tables
            </div>
          </div>

          {filteredTables.map(table => (
            <div
              key={table.id}
              style={getTableStyle(table)}
              onClick={(e) => handleTableClick(table, e)}
              onMouseDown={(e) => handleDragStart(e, table)}
              onTouchStart={(e) => handleDragStart(e, table)}
            >
              {table.number}
            </div>
          ))}

          {isDragging && (
            <div className="absolute inset-0 cursor-move bg-transparent z-10"></div>
          )}
        </div>
      </div>

      {/* Add Table Dialog - Responsive */}
      <Dialog open={isAddTableOpen} onOpenChange={setIsAddTableOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Add New Table</DialogTitle>
          </DialogHeader>
          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label htmlFor="capacity" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Capacity
                </label>
                <Input
                  id="capacity"
                  type="number"
                  min="1"
                  value={tableForm.capacity}
                  onChange={(e) => setTableForm({...tableForm, capacity: parseInt(e.target.value) || 1})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
              <div>
                <label htmlFor="shape" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Shape
                </label>
                <Select
                  value={tableForm.shape}
                  onValueChange={(value) => setTableForm({...tableForm, shape: value})}
                >
                  <SelectTrigger className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm">
                    <SelectValue placeholder="Select shape" />
                  </SelectTrigger>
                  <SelectContent>
                    {tableShapes.map(shape => (
                      <SelectItem key={shape.id} value={shape.id}>
                        <div className="flex items-center">
                          {shape.icon}
                          <span className="ml-2">{shape.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label htmlFor="width" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Width (px)
                </label>
                <Input
                  id="width"
                  type="number"
                  min="40"
                  value={tableForm.width}
                  onChange={(e) => setTableForm({...tableForm, width: parseInt(e.target.value) || 40})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
              <div>
                <label htmlFor="height" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Height (px)
                </label>
                <Input
                  id="height"
                  type="number"
                  min="40"
                  value={tableForm.height}
                  onChange={(e) => setTableForm({...tableForm, height: parseInt(e.target.value) || 40})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsAddTableOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddTable}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Add Table
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Table Dialog - Responsive */}
      <Dialog open={isEditTableOpen} onOpenChange={setIsEditTableOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Edit Table {selectedTable?.number}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label htmlFor="edit-capacity" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Capacity
                </label>
                <Input
                  id="edit-capacity"
                  type="number"
                  min="1"
                  value={tableForm.capacity}
                  onChange={(e) => setTableForm({...tableForm, capacity: parseInt(e.target.value) || 1})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
              <div>
                <label htmlFor="edit-shape" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Shape
                </label>
                <Select
                  value={tableForm.shape}
                  onValueChange={(value) => setTableForm({...tableForm, shape: value})}
                >
                  <SelectTrigger className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm">
                    <SelectValue placeholder="Select shape" />
                  </SelectTrigger>
                  <SelectContent>
                    {tableShapes.map(shape => (
                      <SelectItem key={shape.id} value={shape.id}>
                        <div className="flex items-center">
                          {shape.icon}
                          <span className="ml-2">{shape.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
              <div>
                <label htmlFor="edit-width" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Width (px)
                </label>
                <Input
                  id="edit-width"
                  type="number"
                  min="40"
                  value={tableForm.width}
                  onChange={(e) => setTableForm({...tableForm, width: parseInt(e.target.value) || 40})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
              <div>
                <label htmlFor="edit-height" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                  Height (px)
                </label>
                <Input
                  id="edit-height"
                  type="number"
                  min="40"
                  value={tableForm.height}
                  onChange={(e) => setTableForm({...tableForm, height: parseInt(e.target.value) || 40})}
                  className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
                />
              </div>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsEditTableOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditTable}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Manage Areas Dialog */}
      <Dialog open={isManageAreasOpen} onOpenChange={setIsManageAreasOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Manage Areas</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm text-[#8a745c]">
              Add, edit, or delete areas in your restaurant layout.
            </DialogDescription>
          </DialogHeader>

          <div className="py-3 sm:py-4">
            <div className="space-y-2">
              {areas.map(area => {
                const tablesInArea = tables.filter(table => table.location === area.id).length;
                return (
                  <div
                    key={area.id}
                    className="flex items-center justify-between p-2 sm:p-3 bg-[#f1edea] rounded-md"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium text-sm sm:text-base">{area.name}</span>
                      <span className="text-xs text-[#8a745c]">
                        {tablesInArea} {tablesInArea === 1 ? 'table' : 'tables'}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs border-[#e2dcd4]"
                        onClick={() => {
                          setSelectedArea(area.id);
                          setIsManageAreasOpen(false);
                        }}
                      >
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => openEditAreaDialog(area)}
                      >
                        <Edit className="h-4 w-4 text-[#8a745c]" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => openDeleteAreaDialog(area)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>

            {areas.length === 0 && (
              <div className="text-center py-6 text-[#8a745c]">
                <p>No areas found. Add your first area to get started.</p>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsManageAreasOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Close
            </Button>
            <Button
              onClick={() => {
                setAreaForm({ name: '' });
                setIsAddAreaOpen(true);
                setIsManageAreasOpen(false);
              }}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Area
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Area Dialog */}
      <Dialog open={isAddAreaOpen} onOpenChange={setIsAddAreaOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Add New Area</DialogTitle>
          </DialogHeader>

          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div>
              <label htmlFor="area-name" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Area Name
              </label>
              <Input
                id="area-name"
                value={areaForm.name}
                onChange={(e) => setAreaForm({ ...areaForm, name: e.target.value })}
                placeholder="e.g., Outdoor Terrace"
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
            </div>
            <div>
              <label htmlFor="area-floor" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor
              </label>
              <Select
                value={areaForm.floorId}
                onValueChange={(value) => setAreaForm({ ...areaForm, floorId: value })}
              >
                <SelectTrigger className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm" id="area-floor">
                  <SelectValue placeholder="Select a floor" />
                </SelectTrigger>
                <SelectContent>
                  {floors.map(floor => (
                    <SelectItem key={floor.id} value={floor.id}>
                      {floor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsAddAreaOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddArea}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Add Area
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Area Dialog */}
      <Dialog open={isEditAreaOpen} onOpenChange={setIsEditAreaOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Edit Area</DialogTitle>
          </DialogHeader>

          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div>
              <label htmlFor="edit-area-name" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Area Name
              </label>
              <Input
                id="edit-area-name"
                value={areaForm.name}
                onChange={(e) => setAreaForm({ ...areaForm, name: e.target.value })}
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
            </div>
            <div>
              <label htmlFor="edit-area-floor" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor
              </label>
              <Select
                value={areaForm.floorId}
                onValueChange={(value) => setAreaForm({ ...areaForm, floorId: value })}
              >
                <SelectTrigger className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm" id="edit-area-floor">
                  <SelectValue placeholder="Select a floor" />
                </SelectTrigger>
                <SelectContent>
                  {floors.map(floor => (
                    <SelectItem key={floor.id} value={floor.id}>
                      {floor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsEditAreaOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditArea}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Area Dialog */}
      <Dialog open={isDeleteAreaOpen} onOpenChange={setIsDeleteAreaOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Delete Area</DialogTitle>
          </DialogHeader>

          <div className="py-3 sm:py-4">
            <p className="text-sm">
              Are you sure you want to delete the area "{selectedAreaForEdit?.name}"?
            </p>

            {tables.filter(table => table.location === selectedAreaForEdit?.id).length > 0 && (
              <div className="mt-2 p-3 bg-red-50 text-red-800 rounded-md text-xs sm:text-sm">
                <p className="font-medium">Warning:</p>
                <p>
                  This area contains {tables.filter(table => table.location === selectedAreaForEdit?.id).length} table(s).
                  Deleting this area will also delete all tables in it.
                </p>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteAreaOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteArea}
              className="bg-red-500 text-white hover:bg-red-600 w-full sm:w-auto text-sm"
            >
              Delete Area
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Manage Floors Dialog */}
      <Dialog open={isManageFloorsOpen} onOpenChange={setIsManageFloorsOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Manage Floors</DialogTitle>
            <DialogDescription className="text-xs sm:text-sm text-[#8a745c]">
              Add, edit, or delete floors in your restaurant layout.
            </DialogDescription>
          </DialogHeader>

          <div className="py-3 sm:py-4">
            <div className="space-y-2">
              {floors.map(floor => {
                const areasInFloor = areas.filter(area => area.floorId === floor.id).length;
                return (
                  <div
                    key={floor.id}
                    className="flex items-center justify-between p-2 sm:p-3 bg-[#f1edea] rounded-md"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium text-sm sm:text-base">{floor.name}</span>
                      <span className="text-xs text-[#8a745c]">
                        {areasInFloor} {areasInFloor === 1 ? 'area' : 'areas'}
                      </span>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs border-[#e2dcd4]"
                        onClick={() => {
                          handleFloorChange(floor.id);
                          setIsManageFloorsOpen(false);
                        }}
                      >
                        View
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => openEditFloorDialog(floor)}
                      >
                        <Edit className="h-4 w-4 text-[#8a745c]" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0"
                        onClick={() => openDeleteFloorDialog(floor)}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>

            {floors.length === 0 && (
              <div className="text-center py-6 text-[#8a745c]">
                <p>No floors found. Add your first floor to get started.</p>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsManageFloorsOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Close
            </Button>
            <Button
              onClick={() => {
                setFloorForm({ name: '', order: Math.max(...floors.map(f => f.order), 0) + 1 });
                setIsAddFloorOpen(true);
                setIsManageFloorsOpen(false);
              }}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add New Floor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Floor Dialog */}
      <Dialog open={isAddFloorOpen} onOpenChange={setIsAddFloorOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Add New Floor</DialogTitle>
          </DialogHeader>

          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div>
              <label htmlFor="floor-name" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor Name
              </label>
              <Input
                id="floor-name"
                value={floorForm.name}
                onChange={(e) => setFloorForm({ ...floorForm, name: e.target.value })}
                placeholder="e.g., Ground Floor"
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
            </div>
            <div>
              <label htmlFor="floor-order" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor Order
              </label>
              <Input
                id="floor-order"
                type="number"
                min="1"
                value={floorForm.order}
                onChange={(e) => setFloorForm({ ...floorForm, order: parseInt(e.target.value) || 1 })}
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
              <p className="text-xs text-[#8a745c] mt-1">
                Lower numbers appear first in the list
              </p>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsAddFloorOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddFloor}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Add Floor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Floor Dialog */}
      <Dialog open={isEditFloorOpen} onOpenChange={setIsEditFloorOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Edit Floor</DialogTitle>
          </DialogHeader>

          <div className="grid gap-3 sm:gap-4 py-3 sm:py-4">
            <div>
              <label htmlFor="edit-floor-name" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor Name
              </label>
              <Input
                id="edit-floor-name"
                value={floorForm.name}
                onChange={(e) => setFloorForm({ ...floorForm, name: e.target.value })}
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
            </div>
            <div>
              <label htmlFor="edit-floor-order" className="block text-xs sm:text-sm font-medium text-[#181510] mb-1">
                Floor Order
              </label>
              <Input
                id="edit-floor-order"
                type="number"
                min="1"
                value={floorForm.order}
                onChange={(e) => setFloorForm({ ...floorForm, order: parseInt(e.target.value) || 1 })}
                className="bg-white border-[#e2dcd4] h-8 sm:h-9 text-sm"
              />
              <p className="text-xs text-[#8a745c] mt-1">
                Lower numbers appear first in the list
              </p>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsEditFloorOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditFloor}
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full sm:w-auto text-sm"
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Floor Dialog */}
      <Dialog open={isDeleteFloorOpen} onOpenChange={setIsDeleteFloorOpen}>
        <DialogContent className="bg-[#fbfaf9] border-[#e2dcd4] w-[95%] max-w-md mx-auto p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle className="text-base sm:text-lg">Delete Floor</DialogTitle>
          </DialogHeader>

          <div className="py-3 sm:py-4">
            <p className="text-sm">
              Are you sure you want to delete the floor "{selectedFloorForEdit?.name}"?
            </p>

            {areas.filter(area => area.floorId === selectedFloorForEdit?.id).length > 0 && (
              <div className="mt-2 p-3 bg-red-50 text-red-800 rounded-md text-xs sm:text-sm">
                <p className="font-medium">Warning:</p>
                <p>
                  This floor contains {areas.filter(area => area.floorId === selectedFloorForEdit?.id).length} area(s).
                  Deleting this floor will also delete all areas and tables in it.
                </p>
              </div>
            )}
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setIsDeleteFloorOpen(false)}
              className="border-[#e2dcd4] w-full sm:w-auto text-sm"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteFloor}
              className="bg-red-500 text-white hover:bg-red-600 w-full sm:w-auto text-sm"
            >
              Delete Floor
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
