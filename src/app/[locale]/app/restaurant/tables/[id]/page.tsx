'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { format, parseISO, isToday, isFuture } from 'date-fns';
import { ArrowLeft, Edit, QrCode, Calendar, Users, MapPin, Clock, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import QRCode from 'qrcode';
import ReservationForm from '@/components/restaurant/ReservationForm';

// Mock data for a single table (will be replaced with real data from API)
const mockTable = {
  id: '1',
  number: 1,
  capacity: 4,
  status: 'available',
  location: 'dining',
  image: 'https://lh3.googleusercontent.com/aida-public/AB6AXuCBwbjW6nxCuPdBRW9fDC5h5CeYIO0b4g3pi0JP4jRZ91Lg4NgJSwevVyP3K5Zc4uzYWXPMlL1Ru_lerFb8NRTn1gDYYrnwqzkZq0ZH_h1It93buszWUF0m6kbL9CDZozFGcSofl9DfHuQPtpft3mFOxS5YU3DPOwfmTcBbCOoR9kU_UW4yOrYFt4TOiOmvZHXdjuyvrYTmr7mCkTKT-uY1ittIruQ4GTqvXM7E-p_ZrjGOaaG7J-sr3_3e2AQAeLIpwLfqRKloSg7V'
};

// Mock data for reservations for this table
const mockReservations = [
  {
    id: 'res1',
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '18:00',
    partySize: 2,
    tableId: '1',
    status: 'confirmed',
    specialRequests: 'No nuts please',
  },
  {
    id: 'res2',
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(Date.now() + 86400000), 'yyyy-MM-dd'), // Tomorrow
    time: '19:30',
    partySize: 4,
    tableId: '1',
    status: 'pending',
    specialRequests: '',
  },
  {
    id: 'res3',
    customerName: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(Date.now() + 172800000), 'yyyy-MM-dd'), // Day after tomorrow
    time: '20:00',
    partySize: 3,
    tableId: '1',
    status: 'confirmed',
    specialRequests: 'Birthday celebration',
  },
];

export default function TableDetailPage() {
  const params = useParams();
  const router = useRouter();
  const tableId = params.id as string;
  
  // State for UI
  const [table, setTable] = useState(mockTable);
  const [reservations, setReservations] = useState(mockReservations);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('details');
  const [isAddReservationOpen, setIsAddReservationOpen] = useState(false);
  
  // In a real app, we would fetch the table data from the API
  useEffect(() => {
    // Simulate API call to get table details
    // In a real app, this would be replaced with an API call
    // const fetchTable = async () => {
    //   const response = await fetch(`/api/tables/${tableId}`);
    //   const data = await response.json();
    //   setTable(data);
    // };
    // fetchTable();
    
    // Generate QR code for this table
    generateQRCode();
  }, [tableId]);
  
  // Generate QR code for this table
  const generateQRCode = async () => {
    try {
      const baseUrl = window.location.origin;
      // In a real app, this would be a URL to your ordering system with the table ID
      const orderUrl = `${baseUrl}/order?tableId=${table.id}&tableNumber=${table.number}`;
      
      // Generate QR code as data URL
      const qrCodeUrl = await QRCode.toDataURL(orderUrl, {
        width: 200,
        margin: 2,
        color: {
          dark: '#181510', // QR code color
          light: '#fbfaf9' // Background color
        }
      });
      
      setQrCodeUrl(qrCodeUrl);
    } catch (error) {
      console.error('Error generating QR code:', error);
    }
  };
  
  // Download QR code
  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `table-${table.number}-qr-code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Available</Badge>;
      case 'occupied':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Occupied</Badge>;
      case 'reserved':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Reserved</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };
  
  // Get reservation status badge
  const getReservationStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Confirmed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };
  
  // Filter upcoming reservations
  const upcomingReservations = reservations.filter(reservation => {
    const reservationDate = parseISO(reservation.date);
    return isFuture(reservationDate) || 
      (isToday(reservationDate) && reservation.time >= format(new Date(), 'HH:mm'));
  });
  
  return (
    <div className="p-6 font-be-vietnam">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Table {table.number}</h1>
          <p className="text-[#8a745c] text-sm">Manage table details, reservations, and QR code</p>
        </div>
        <Link href="/app/restaurant/tables">
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Tables
          </Button>
        </Link>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
          <TabsTrigger
            value="details"
            className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">Details</p>
          </TabsTrigger>
          <TabsTrigger
            value="reservations"
            className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">Reservations</p>
          </TabsTrigger>
          <TabsTrigger
            value="qr-code"
            className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
          >
            <p className="text-sm font-bold leading-normal tracking-[0.015em]">QR Code</p>
          </TabsTrigger>
        </TabsList>
        
        {/* Details Tab */}
        <TabsContent value="details" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <div className="bg-[#fbfaf9] rounded-lg overflow-hidden border border-[#e2dcd4]">
                <div className="aspect-video bg-cover bg-center"
                  style={{
                    backgroundImage: `linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0) 100%), url("${table.image}")`
                  }}
                >
                </div>
                <div className="p-6">
                  <h2 className="text-[#181510] text-xl font-bold mb-4">Table Information</h2>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center gap-2">
                      <Users className="text-[#8a745c] h-5 w-5" />
                      <div>
                        <p className="text-[#8a745c] text-sm">Capacity</p>
                        <p className="text-[#181510] font-medium">{table.capacity} people</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <MapPin className="text-[#8a745c] h-5 w-5" />
                      <div>
                        <p className="text-[#8a745c] text-sm">Location</p>
                        <p className="text-[#181510] font-medium capitalize">{table.location} Area</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <AlertCircle className="text-[#8a745c] h-5 w-5" />
                      <div>
                        <p className="text-[#8a745c] text-sm">Status</p>
                        <div className="mt-1">{getStatusBadge(table.status)}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Clock className="text-[#8a745c] h-5 w-5" />
                      <div>
                        <p className="text-[#8a745c] text-sm">Next Reservation</p>
                        <p className="text-[#181510] font-medium">
                          {upcomingReservations.length > 0 
                            ? `${format(parseISO(upcomingReservations[0].date), 'MMM d')} at ${upcomingReservations[0].time}`
                            : 'No upcoming reservations'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-1">
              <div className="bg-[#fbfaf9] rounded-lg border border-[#e2dcd4] p-6">
                <h2 className="text-[#181510] text-xl font-bold mb-4">Actions</h2>
                
                <div className="space-y-3">
                  <Button className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] justify-start">
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Table
                  </Button>
                  
                  <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
                    <DialogTrigger asChild>
                      <Button className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] justify-start">
                        <Calendar className="h-4 w-4 mr-2" />
                        Add Reservation
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-3xl">
                      <DialogHeader>
                        <DialogTitle>Add New Reservation for Table {table.number}</DialogTitle>
                      </DialogHeader>
                      <ReservationForm
                        tables={[{ id: table.id, number: table.number }]}
                        initialData={{ tableId: table.id } as any}
                        onSuccess={() => setIsAddReservationOpen(false)}
                        onCancel={() => setIsAddReservationOpen(false)}
                      />
                    </DialogContent>
                  </Dialog>
                  
                  <Button 
                    className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] justify-start"
                    onClick={() => setActiveTab('qr-code')}
                  >
                    <QrCode className="h-4 w-4 mr-2" />
                    View QR Code
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
        
        {/* Reservations Tab */}
        <TabsContent value="reservations" className="mt-6">
          <div className="bg-[#fbfaf9] rounded-lg overflow-hidden border border-[#e2dcd4]">
            <div className="p-6 flex justify-between items-center border-b border-[#e2dcd4]">
              <h2 className="text-[#181510] text-xl font-bold">Reservations for Table {table.number}</h2>
              
              <Dialog open={isAddReservationOpen} onOpenChange={setIsAddReservationOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
                    Add Reservation
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-3xl">
                  <DialogHeader>
                    <DialogTitle>Add New Reservation for Table {table.number}</DialogTitle>
                  </DialogHeader>
                  <ReservationForm
                    tables={[{ id: table.id, number: table.number }]}
                    initialData={{ tableId: table.id } as any}
                    onSuccess={() => setIsAddReservationOpen(false)}
                    onCancel={() => setIsAddReservationOpen(false)}
                  />
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-[#f1edea] text-[#181510]">
                  <tr>
                    <th className="px-4 py-2 text-left">Customer</th>
                    <th className="px-4 py-2 text-left">Date & Time</th>
                    <th className="px-4 py-2 text-left">Party Size</th>
                    <th className="px-4 py-2 text-left">Status</th>
                    <th className="px-4 py-2 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {reservations.length > 0 ? (
                    reservations.map((reservation) => (
                      <tr key={reservation.id} className="border-b border-[#e2dcd4]">
                        <td className="px-4 py-3">
                          <div className="font-medium">{reservation.customerName}</div>
                          <div className="text-sm text-[#8a745c]">{reservation.customerEmail}</div>
                        </td>
                        <td className="px-4 py-3">
                          <div>{format(parseISO(reservation.date), 'MMM d, yyyy')}</div>
                          <div className="text-sm text-[#8a745c]">{reservation.time}</div>
                        </td>
                        <td className="px-4 py-3">{reservation.partySize} people</td>
                        <td className="px-4 py-3">{getReservationStatusBadge(reservation.status)}</td>
                        <td className="px-4 py-3">
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs h-8 px-2 border-[#e2dcd4]"
                          >
                            View Details
                          </Button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-4 py-3 text-center text-[#8a745c]">
                        No reservations found for this table
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>
        
        {/* QR Code Tab */}
        <TabsContent value="qr-code" className="mt-6">
          <div className="bg-[#fbfaf9] rounded-lg overflow-hidden border border-[#e2dcd4] p-6">
            <h2 className="text-[#181510] text-xl font-bold mb-4">Table {table.number} QR Code</h2>
            <p className="text-[#8a745c] mb-6">
              Display this QR code on the table to allow customers to scan and order directly from their phones.
            </p>
            
            <div className="flex flex-col md:flex-row gap-6 items-center">
              <div className="bg-white p-6 rounded-lg border border-[#e2dcd4] flex items-center justify-center">
                {qrCodeUrl ? (
                  <img 
                    src={qrCodeUrl} 
                    alt={`Table ${table.number} QR Code`} 
                    className="w-48 h-48"
                  />
                ) : (
                  <div className="w-48 h-48 flex items-center justify-center text-[#8a745c]">
                    Loading QR code...
                  </div>
                )}
              </div>
              
              <div className="flex flex-col gap-4">
                <div>
                  <h3 className="text-[#181510] font-bold mb-1">Table Information</h3>
                  <p className="text-[#8a745c]">Table Number: {table.number}</p>
                  <p className="text-[#8a745c]">Location: {table.location} Area</p>
                  <p className="text-[#8a745c]">Capacity: {table.capacity} people</p>
                </div>
                
                <Button 
                  className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e] w-full md:w-auto"
                  onClick={downloadQRCode}
                  disabled={!qrCodeUrl}
                >
                  Download QR Code
                </Button>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
