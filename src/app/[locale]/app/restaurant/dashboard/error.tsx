'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex flex-col items-center justify-center p-8 text-center">
      <h2 className="mb-4 text-2xl font-bold text-[#181510]">Something went wrong</h2>
      <p className="mb-6 text-[#8a745c]">
        We encountered an error while loading the dashboard. Please try again later.
      </p>
      <div className="flex gap-4">
        <button
          onClick={reset}
          className="rounded-lg bg-[#8a745c] px-4 py-2 text-white hover:bg-[#6a5a48]"
        >
          Try again
        </button>
        <Link
          href="/app"
          className="rounded-lg border border-[#e2dcd4] bg-[#fbfaf9] px-4 py-2 text-[#181510] hover:bg-[#f1edea]"
        >
          Go back to main dashboard
        </Link>
      </div>
    </div>
  );
}
