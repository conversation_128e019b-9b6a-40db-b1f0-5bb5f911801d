'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Mock data for sales trends
const salesTrendsData = [
  { name: 'Week 1', sales: 18500 },
  { name: 'Week 2', sales: 12300 },
  { name: 'Week 3', sales: 15800 },
  { name: 'Week 4', sales: 25450 },
];

// Mock data for popular menu items
const popularMenuItems = [
  {
    id: '1',
    name: 'Spicy Chicken Sandwich',
    category: 'Main Course',
    orders: 350,
    revenue: 3500,
  },
  {
    id: '2',
    name: 'Avocado Toast',
    category: 'Appetizer',
    orders: 280,
    revenue: 2240,
  },
  {
    id: '3',
    name: 'Classic Burger',
    category: 'Main Course',
    orders: 250,
    revenue: 2500,
  },
  {
    id: '4',
    name: 'Caesar Salad',
    category: 'Appetizer',
    orders: 220,
    revenue: 1760,
  },
  {
    id: '5',
    name: 'Chocolate Brownie',
    category: 'Dessert',
    orders: 200,
    revenue: 1200,
  },
];

// Mock data for customer data
const customerData = {
  totalCustomers: 1200,
  averageSpend: 21.21,
  repeatCustomers: 45,
};

export default function ReportsPage() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const t = useTranslations('restaurant');
  const [activeTab, setActiveTab] = useState('sales-trends');

  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-[#fbfaf9] p-3 border border-[#e2dcd4] rounded-md shadow-sm">
          <p className="text-[#181510] font-medium">{`${label}`}</p>
          <p className="text-[#8a745c]">{`Sales: $${payload[0].value.toLocaleString()}`}</p>
        </div>
      );
    }
    return null;
  };

  // Render the Sales Trends tab content
  const renderSalesTrends = () => {
    return (
      <>
        <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Sales Trends</h2>
        <div className="flex flex-wrap gap-4 px-4 py-6">
          <div className="flex min-w-72 flex-1 flex-col gap-2 rounded-lg border border-[#e2dcd4] p-6">
            <p className="text-[#181510] text-base font-medium leading-normal">Total Sales</p>
            <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight truncate">$25,450</p>
            <div className="flex gap-1">
              <p className="text-[#8a745c] text-base font-normal leading-normal">Last 30 Days</p>
              <p className="text-[#07880e] text-base font-medium leading-normal">+12%</p>
            </div>
            <div className="flex min-h-[180px] flex-1 flex-col gap-8 py-4">
              <ResponsiveContainer width="100%" height={148}>
                <LineChart data={salesTrendsData} margin={{ top: 5, right: 20, left: 10, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#e2dcd4" />
                  <XAxis dataKey="name" tick={{ fill: '#8a745c', fontSize: 13 }} />
                  <YAxis tick={{ fill: '#8a745c', fontSize: 13 }} hide />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="sales"
                    stroke="#8a745c"
                    strokeWidth={3}
                    dot={{ r: 4, fill: '#8a745c' }}
                    activeDot={{ r: 6, fill: '#8a745c' }}
                  />
                </LineChart>
              </ResponsiveContainer>
              <div className="flex justify-around">
                <p className="text-[#8a745c] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 1</p>
                <p className="text-[#8a745c] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 2</p>
                <p className="text-[#8a745c] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 3</p>
                <p className="text-[#8a745c] text-[13px] font-bold leading-normal tracking-[0.015em]">Week 4</p>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  };

  // Render the Popular Items tab content
  const renderPopularItems = () => {
    return (
      <>
        <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Popular Menu Items</h2>
        <div className="px-4 py-3 @container">
          <div className="flex overflow-hidden rounded-lg border border-[#e2dcd4] bg-[#fbfaf9]">
            <table className="flex-1">
              <thead>
                <tr className="bg-[#fbfaf9]">
                  <th className="table-column-120 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Item</th>
                  <th className="table-column-240 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Category</th>
                  <th className="table-column-360 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Orders</th>
                  <th className="table-column-480 px-4 py-3 text-left text-[#181510] w-[400px] text-sm font-medium leading-normal">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {popularMenuItems.map((item) => (
                  <tr key={item.id} className="border-t border-t-[#e2dcd4]">
                    <td className="table-column-120 h-[72px] px-4 py-2 w-[400px] text-[#181510] text-sm font-normal leading-normal">
                      {item.name}
                    </td>
                    <td className="table-column-240 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">
                      {item.category}
                    </td>
                    <td className="table-column-360 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">{item.orders}</td>
                    <td className="table-column-480 h-[72px] px-4 py-2 w-[400px] text-[#8a745c] text-sm font-normal leading-normal">${item.revenue.toLocaleString()}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <style dangerouslySetInnerHTML={{
            __html: `
              @container(max-width:120px){.table-column-120{display: none;}}
              @container(max-width:240px){.table-column-240{display: none;}}
              @container(max-width:360px){.table-column-360{display: none;}}
              @container(max-width:480px){.table-column-480{display: none;}}
            `
          }} />
        </div>
      </>
    );
  };

  // Render the Customer Data tab content
  const renderCustomerData = () => {
    return (
      <>
        <h2 className="text-[#181510] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Customer Data</h2>
        <div className="flex flex-wrap gap-4 p-4">
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-[#e2dcd4]">
            <p className="text-[#181510] text-base font-medium leading-normal">Total Customers</p>
            <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight">{customerData.totalCustomers.toLocaleString()}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-[#e2dcd4]">
            <p className="text-[#181510] text-base font-medium leading-normal">Average Spend</p>
            <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight">${customerData.averageSpend.toFixed(2)}</p>
          </div>
          <div className="flex min-w-[158px] flex-1 flex-col gap-2 rounded-lg p-6 border border-[#e2dcd4]">
            <p className="text-[#181510] text-base font-medium leading-normal">Repeat Customers</p>
            <p className="text-[#181510] tracking-light text-2xl font-bold leading-tight">{customerData.repeatCustomers}%</p>
          </div>
        </div>
      </>
    );
  };

  // Render the appropriate content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'sales-trends':
        return renderSalesTrends();
      case 'popular-items':
        return renderPopularItems();
      case 'customer-data':
        return renderCustomerData();
      default:
        return renderSalesTrends();
    }
  };

  return (
    <>
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Reports & Analytics</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Gain insights into your restaurant's performance with detailed reports and analytics.</p>
        </div>
      </div>

      <div className="pb-3">
        <div className="flex border-b border-[#e2dcd4] px-4 gap-8">
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'sales-trends' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('sales-trends'); }}
          >
            <p className={`${activeTab === 'sales-trends' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Sales Trends</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'popular-items' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('popular-items'); }}
          >
            <p className={`${activeTab === 'popular-items' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Popular Items</p>
          </a>
          <a
            className={`flex flex-col items-center justify-center border-b-[3px] ${activeTab === 'customer-data' ? 'border-b-[#e5ccb2] text-[#181510]' : 'border-b-transparent text-[#8a745c]'} pb-[13px] pt-4`}
            href="#"
            onClick={(e) => { e.preventDefault(); setActiveTab('customer-data'); }}
          >
            <p className={`${activeTab === 'customer-data' ? 'text-[#181510]' : 'text-[#8a745c]'} text-sm font-bold leading-normal tracking-[0.015em]`}>Customer Data</p>
          </a>
        </div>
      </div>

      {/* Render the content based on the active tab */}
      {renderTabContent()}
    </>
  );
}
