'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { CheckCheck, Search, Trash2 } from 'lucide-react';
import NotificationItem from '@/components/notifications/NotificationItem';
import mockNotifications, { Notification } from '@/mock/notificationData';
import { toast } from 'sonner';

export default function NotificationsPage() {
  // In a real app, we would fetch notifications from an API
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Count notifications by status
  const allCount = notifications.length;
  const unreadCount = notifications.filter(notification => !notification.isRead).length;
  const readCount = notifications.filter(notification => notification.isRead).length;

  // Filter notifications based on active tab and search term
  const filteredNotifications = notifications.filter(notification => {
    // Filter by tab
    if (activeTab === 'unread' && notification.isRead) return false;
    if (activeTab === 'read' && !notification.isRead) return false;

    // Filter by search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        notification.title.toLowerCase().includes(searchLower) ||
        notification.message.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  // Mark a notification as read
  const markAsRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, isRead: true }))
    );
    toast.success('All notifications marked as read');
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
    toast.success('All notifications cleared');
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Notifications</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">View and manage your notifications</p>
        </div>
        <div className="flex items-end gap-3">
          <Input
            placeholder="Search notifications..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64 bg-[#fbfaf9] border-[#e5e1dc]"
            startIcon={<Search className="h-4 w-4 text-[#8a745c]" />}
          />
        </div>
      </div>

      <div className="bg-[#fbfaf9] rounded-lg border border-[#e5e1dc] overflow-hidden">
        <div className="p-4 border-b border-[#e5e1dc] flex flex-wrap justify-between items-center gap-4">
          <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-auto">
            <TabsList className="bg-[#f1edea]">
              <TabsTrigger value="all" className="data-[state=active]:bg-[#fbfaf9]">
                All
                <Badge className="ml-2 bg-[#e5e1dc] text-[#181510] hover:bg-[#e5e1dc]">{allCount}</Badge>
              </TabsTrigger>
              <TabsTrigger value="unread" className="data-[state=active]:bg-[#fbfaf9]">
                Unread
                <Badge className="ml-2 bg-[#e5e1dc] text-[#181510] hover:bg-[#e5e1dc]">{unreadCount}</Badge>
              </TabsTrigger>
              <TabsTrigger value="read" className="data-[state=active]:bg-[#fbfaf9]">
                Read
                <Badge className="ml-2 bg-[#e5e1dc] text-[#181510] hover:bg-[#e5e1dc]">{readCount}</Badge>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-0">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-64 text-[#8a745c]">
                  {searchTerm ? 'No notifications match your search' : 'No notifications'}
                </div>
              ) : (
                <div className="divide-y divide-[#e5e1dc]">
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                    />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="unread" className="mt-0">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-64 text-[#8a745c]">
                  {searchTerm ? 'No unread notifications match your search' : 'No unread notifications'}
                </div>
              ) : (
                <div className="divide-y divide-[#e5e1dc]">
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                    />
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="read" className="mt-0">
              {filteredNotifications.length === 0 ? (
                <div className="flex items-center justify-center h-64 text-[#8a745c]">
                  {searchTerm ? 'No read notifications match your search' : 'No read notifications'}
                </div>
              ) : (
                <div className="divide-y divide-[#e5e1dc]">
                  {filteredNotifications.map((notification) => (
                    <NotificationItem
                      key={notification.id}
                      notification={notification}
                      onMarkAsRead={markAsRead}
                    />
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>

          <div className="flex gap-2">
            {unreadCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-8 border-[#e5e1dc]"
                onClick={markAllAsRead}
              >
                <CheckCheck className="h-3.5 w-3.5 mr-1" />
                Mark all as read
              </Button>
            )}
            {allCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="text-xs h-8 border-[#e5e1dc] hover:bg-red-50 hover:text-red-600 hover:border-red-200"
                onClick={clearAllNotifications}
              >
                <Trash2 className="h-3.5 w-3.5 mr-1" />
                Clear all
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
