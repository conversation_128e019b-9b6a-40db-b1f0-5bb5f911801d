'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import ReservationForm from '@/components/restaurant/ReservationForm';
import { toast } from 'sonner';

// Mock data for tables
const mockTables = [
  { id: 'table1', number: 1, capacity: 2, status: 'available', location: 'dining' },
  { id: 'table2', number: 2, capacity: 4, status: 'available', location: 'dining' },
  { id: 'table3', number: 3, capacity: 6, status: 'available', location: 'dining' },
  { id: 'table4', number: 4, capacity: 2, status: 'available', location: 'outdoor' },
  { id: 'table5', number: 5, capacity: 4, status: 'available', location: 'outdoor' },
];

// Mock data for reservations
const mockReservations = [
  {
    id: 'res1',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '18:00',
    partySize: 2,
    tableId: 'table1',
    status: 'confirmed',
    specialRequests: 'No nuts please',
  },
  {
    id: 'res2',
    customerName: '<PERSON> <PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '19:30',
    partySize: 4,
    tableId: 'table2',
    status: 'pending',
    specialRequests: '',
  },
  {
    id: 'res3',
    customerName: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-16',
    time: '20:00',
    partySize: 6,
    tableId: 'table3',
    status: 'confirmed',
    specialRequests: 'Birthday celebration',
  },
  {
    id: 'res4',
    customerName: 'Alice Brown',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '18:30',
    partySize: 2,
    tableId: 'table4',
    status: 'confirmed',
    specialRequests: '',
  },
  {
    id: 'res5',
    customerName: 'Charlie Wilson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '19:00',
    partySize: 4,
    tableId: 'table5',
    status: 'pending',
    specialRequests: 'Outdoor seating preferred',
  },
];

interface EditReservationPageProps {
  params: {
    id: string;
  };
}

export default function EditReservationPage({ params }: EditReservationPageProps) {
  const { id } = params;
  const router = useRouter();
  
  // In a real app, we would use the API
  // const { data: reservation, isLoading } = useGetReservationByIdQuery({ merchantId, reservationId: id });
  const [reservation] = useState(mockReservations.find(res => res.id === id));
  const [tables] = useState(mockTables);
  
  // Handle successful update
  const handleUpdateSuccess = () => {
    toast.success('Reservation updated successfully');
    router.push(`/app/restaurant/reservations/${id}`);
  };
  
  if (!reservation) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="bg-[#f1edea] p-6 rounded-lg text-center">
          <h1 className="text-xl font-medium text-[#181510] mb-4">Reservation Not Found</h1>
          <p className="text-[#8a745c] mb-6">The reservation you're trying to edit doesn't exist or has been removed.</p>
          <Link href="/app/restaurant/reservations">
            <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              Back to Reservations
            </Button>
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#181510]">Edit Reservation</h1>
          <p className="text-[#8a745c]">Update reservation details</p>
        </div>
        <Link href={`/app/restaurant/reservations/${id}`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            Cancel
          </Button>
        </Link>
      </div>
      
      <div className="bg-[#f1edea] p-6 rounded-lg">
        <ReservationForm 
          tables={tables.map(table => ({ id: table.id, number: table.number }))}
          onSuccess={handleUpdateSuccess}
          onCancel={() => router.push(`/app/restaurant/reservations/${id}`)}
          initialData={reservation}
        />
      </div>
    </div>
  );
}
