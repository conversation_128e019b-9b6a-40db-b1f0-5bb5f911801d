'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { format, parseISO } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
  DialogFooter,
} from '@/components/ui/dialog';
import { toast } from 'sonner';

// Mock data for tables
const mockTables = [
  { id: 'table1', number: 1, capacity: 2, status: 'available', location: 'dining' },
  { id: 'table2', number: 2, capacity: 4, status: 'available', location: 'dining' },
  { id: 'table3', number: 3, capacity: 6, status: 'available', location: 'dining' },
  { id: 'table4', number: 4, capacity: 2, status: 'available', location: 'outdoor' },
  { id: 'table5', number: 5, capacity: 4, status: 'available', location: 'outdoor' },
];

// Mock data for reservations
const mockReservations = [
  {
    id: 'res1',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '18:00',
    partySize: 2,
    tableId: 'table1',
    status: 'confirmed',
    specialRequests: 'No nuts please',
  },
  {
    id: 'res2',
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-15',
    time: '19:30',
    partySize: 4,
    tableId: 'table2',
    status: 'pending',
    specialRequests: '',
  },
  {
    id: 'res3',
    customerName: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: '2023-06-16',
    time: '20:00',
    partySize: 6,
    tableId: 'table3',
    status: 'confirmed',
    specialRequests: 'Birthday celebration',
  },
  {
    id: 'res4',
    customerName: 'Alice Brown',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '18:30',
    partySize: 2,
    tableId: 'table4',
    status: 'confirmed',
    specialRequests: '',
  },
  {
    id: 'res5',
    customerName: 'Charlie Wilson',
    customerEmail: '<EMAIL>',
    customerPhone: '************',
    date: format(new Date(), 'yyyy-MM-dd'),
    time: '19:00',
    partySize: 4,
    tableId: 'table5',
    status: 'pending',
    specialRequests: 'Outdoor seating preferred',
  },
];

interface ReservationDetailPageProps {
  params: {
    id: string;
  };
}

export default function ReservationDetailPage({ params }: ReservationDetailPageProps) {
  const { id } = params;
  const router = useRouter();
  
  // In a real app, we would use the API
  // const { data: reservation, isLoading } = useGetReservationByIdQuery({ merchantId, reservationId: id });
  const [reservation, setReservation] = useState(mockReservations.find(res => res.id === id));
  const [tables] = useState(mockTables);
  const [isConfirmCancelOpen, setIsConfirmCancelOpen] = useState(false);
  
  // Get the table
  const table = tables.find(t => t.id === reservation?.tableId);
  
  // Handle reservation cancellation
  const handleCancelReservation = () => {
    // In a real app, we would call the API
    setReservation(prev => prev ? { ...prev, status: 'cancelled' } : prev);
    setIsConfirmCancelOpen(false);
    toast.success('Reservation cancelled');
  };
  
  // Get reservation status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Confirmed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">{status}</Badge>;
    }
  };
  
  if (!reservation) {
    return (
      <div className="p-6 font-be-vietnam">
        <div className="bg-[#f1edea] p-6 rounded-lg text-center">
          <h1 className="text-xl font-medium text-[#181510] mb-4">Reservation Not Found</h1>
          <p className="text-[#8a745c] mb-6">The reservation you're looking for doesn't exist or has been removed.</p>
          <Link href="/app/restaurant/reservations">
            <Button className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]">
              Back to Reservations
            </Button>
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-[#181510]">Reservation Details</h1>
          <p className="text-[#8a745c]">View and manage reservation information</p>
        </div>
        <Link href="/app/restaurant/reservations">
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            Back to Reservations
          </Button>
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="bg-[#f1edea] p-6 rounded-lg">
            <div className="flex justify-between items-start mb-6">
              <h2 className="text-xl font-medium text-[#181510]">
                Reservation for {reservation.customerName}
              </h2>
              {getStatusBadge(reservation.status)}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-sm font-medium text-[#8a745c] mb-1">Customer Information</h3>
                <div className="bg-[#fbfaf9] p-4 rounded-md">
                  <div className="mb-2">
                    <div className="text-sm text-[#8a745c]">Name</div>
                    <div className="font-medium">{reservation.customerName}</div>
                  </div>
                  <div className="mb-2">
                    <div className="text-sm text-[#8a745c]">Email</div>
                    <div className="font-medium">{reservation.customerEmail}</div>
                  </div>
                  <div>
                    <div className="text-sm text-[#8a745c]">Phone</div>
                    <div className="font-medium">{reservation.customerPhone}</div>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-[#8a745c] mb-1">Reservation Details</h3>
                <div className="bg-[#fbfaf9] p-4 rounded-md">
                  <div className="mb-2">
                    <div className="text-sm text-[#8a745c]">Date & Time</div>
                    <div className="font-medium">
                      {format(parseISO(reservation.date), 'MMMM d, yyyy')} at {reservation.time}
                    </div>
                  </div>
                  <div className="mb-2">
                    <div className="text-sm text-[#8a745c]">Party Size</div>
                    <div className="font-medium">{reservation.partySize} people</div>
                  </div>
                  <div>
                    <div className="text-sm text-[#8a745c]">Table</div>
                    <div className="font-medium">
                      Table {table?.number} ({table?.location})
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {reservation.specialRequests && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-[#8a745c] mb-1">Special Requests</h3>
                <div className="bg-[#fbfaf9] p-4 rounded-md">
                  {reservation.specialRequests}
                </div>
              </div>
            )}
            
            <div className="flex justify-end space-x-3">
              <Link href={`/app/restaurant/reservations/${id}/edit`}>
                <Button className="bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border border-[#e2dcd4]">
                  Edit Reservation
                </Button>
              </Link>
              
              {reservation.status !== 'cancelled' && (
                <Dialog open={isConfirmCancelOpen} onOpenChange={setIsConfirmCancelOpen}>
                  <DialogTrigger asChild>
                    <Button className="bg-[#f1edea] text-[#181510] hover:bg-red-50 hover:text-red-600 hover:border-red-200 border border-[#e2dcd4]">
                      Cancel Reservation
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Cancel Reservation</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                      <p className="mb-2">Are you sure you want to cancel this reservation?</p>
                      <p className="text-[#8a745c]">This action cannot be undone.</p>
                    </div>
                    <DialogFooter>
                      <Button 
                        variant="outline" 
                        onClick={() => setIsConfirmCancelOpen(false)}
                        className="border-[#e2dcd4]"
                      >
                        Keep Reservation
                      </Button>
                      <Button 
                        onClick={handleCancelReservation}
                        className="bg-red-500 text-white hover:bg-red-600"
                      >
                        Cancel Reservation
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </div>
        
        <div className="md:col-span-1">
          <div className="bg-[#f1edea] p-6 rounded-lg">
            <h2 className="text-lg font-medium mb-4 text-[#181510]">Actions</h2>
            
            <div className="space-y-3">
              <Button 
                className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
                onClick={() => toast.success('Email confirmation sent')}
              >
                Send Confirmation Email
              </Button>
              
              <Button 
                className="w-full bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border border-[#e2dcd4]"
                onClick={() => toast.success('SMS reminder sent')}
              >
                Send SMS Reminder
              </Button>
              
              <Button 
                className="w-full bg-[#f1edea] text-[#181510] hover:bg-[#e2dcd4] border border-[#e2dcd4]"
                onClick={() => window.print()}
              >
                Print Reservation
              </Button>
            </div>
            
            <h2 className="text-lg font-medium mt-6 mb-4 text-[#181510]">Table Information</h2>
            
            {table && (
              <div className="bg-[#fbfaf9] p-4 rounded-md">
                <div className="mb-2">
                  <div className="text-sm text-[#8a745c]">Table Number</div>
                  <div className="font-medium">{table.number}</div>
                </div>
                <div className="mb-2">
                  <div className="text-sm text-[#8a745c]">Location</div>
                  <div className="font-medium capitalize">{table.location}</div>
                </div>
                <div>
                  <div className="text-sm text-[#8a745c]">Capacity</div>
                  <div className="font-medium">{table.capacity} people</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
