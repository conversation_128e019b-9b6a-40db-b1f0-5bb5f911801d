'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, Store, MapPin, ArrowRight, Users, Calendar } from 'lucide-react';
import { mockShopsWithBranches } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';

export default function RestaurantShopsPage() {
  const [shops, setShops] = useState(mockShopsWithBranches);
  const [isLoading, setIsLoading] = useState(true);

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  return (
    <div className="w-full max-w-7xl mx-auto font-be-vietnam">
      {/* Hero Section */}
      <div className="text-center mb-8">
        <h1 className="text-[#181510] text-3xl md:text-4xl font-bold leading-tight mb-4">
          Your Restaurant Empire
        </h1>
        <p className="text-[#8a745c] text-lg font-normal leading-normal max-w-2xl mx-auto mb-6">
          Manage all your restaurants from one central dashboard. Monitor performance, track orders, and grow your business.
        </p>
        <Link href="/app/restaurant/new">
          <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white px-6 py-3 text-lg">
            <PlusCircle className="h-5 w-5 mr-2" />
            Add New Restaurant
          </Button>
        </Link>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-6 text-center">
            <Store className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
            <div className="text-2xl font-bold text-[#181510]">{shops.length}</div>
            <div className="text-sm text-[#8a745c]">Total Restaurants</div>
          </CardContent>
        </Card>
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-6 text-center">
            <MapPin className="h-8 w-8 text-[#8a745c] mx-auto mb-2" />
            <div className="text-2xl font-bold text-[#181510]">
              {shops.reduce((total, shop) => total + shop.branches.length, 0)}
            </div>
            <div className="text-sm text-[#8a745c]">Total Branches</div>
          </CardContent>
        </Card>
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardContent className="p-6 text-center">
            <div className="h-8 w-8 bg-[#8a745c] rounded-full flex items-center justify-center mx-auto mb-2">
              <span className="text-white text-sm font-bold">$</span>
            </div>
            <div className="text-2xl font-bold text-[#181510]">$24.5K</div>
            <div className="text-sm text-[#8a745c]">Monthly Revenue</div>
          </CardContent>
        </Card>
      </div>

      {/* Restaurants Grid */}
      <div className="mb-6">
        <h2 className="text-[#181510] text-2xl font-bold mb-4">Your Restaurants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
          {shops.map((shop) => (
            <Card key={shop.id} className="overflow-hidden bg-[#fbfaf9] border-[#e5e1dc] hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
              {/* Restaurant Image */}
              <div className="relative">
                <div
                  className="h-48 bg-cover bg-center"
                  style={{ backgroundImage: `url(${shop.logo})` }}
                />
                <div className="absolute top-4 right-4">
                  <div className="bg-[#8a745c] text-white px-2 py-1 rounded-full text-xs font-medium">
                    {shop.branches.length} {shop.branches.length === 1 ? 'Branch' : 'Branches'}
                  </div>
                </div>
              </div>

              {/* Restaurant Info */}
              <CardHeader className="pb-3">
                <CardTitle className="text-[#181510] flex items-center gap-2 text-xl">
                  <Store className="h-5 w-5 text-[#8a745c]" />
                  {shop.name}
                </CardTitle>
                <CardDescription className="text-[#8a745c] text-base">
                  {shop.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Branches List */}
                <div className="mb-4">
                  <h3 className="text-sm font-semibold text-[#181510] mb-3 flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-[#8a745c]" />
                    Locations
                  </h3>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {shop.branches.map((branch) => (
                      <Link
                        key={branch.id}
                        href={`/app/restaurant/${shop.slug}/${branch.slug}/dashboard`}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-[#f1edea] transition-colors group border border-transparent hover:border-[#e5e1dc]"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-2 h-2 bg-[#8a745c] rounded-full"></div>
                          <div>
                            <p className="text-sm font-medium text-[#181510] group-hover:text-[#8a745c]">
                              {branch.name}
                            </p>
                            <p className="text-xs text-[#8a745c]">
                              {branch.address.split(',')[0]}
                            </p>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-[#8a745c] opacity-0 group-hover:opacity-100 transition-opacity" />
                      </Link>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Link href={`/app/restaurant/${shop.slug}`} className="flex-1">
                    <Button
                      variant="outline"
                      className="w-full border-[#e2dcd4] text-[#181510] hover:bg-[#f1edea]"
                    >
                      <Store className="h-4 w-4 mr-2" />
                      Manage
                    </Button>
                  </Link>
                  <Link href={`/app/restaurant/${shop.slug}/${shop.branches[0]?.slug}/dashboard`} className="flex-1">
                    <Button
                      className="w-full bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                    >
                      <ArrowRight className="h-4 w-4 mr-2" />
                      Open
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Empty State */}
      {shops.length === 0 && (
        <div className="text-center py-12">
          <Store className="h-16 w-16 text-[#8a745c] mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-semibold text-[#181510] mb-2">No restaurants yet</h3>
          <p className="text-[#8a745c] mb-6">Get started by adding your first restaurant</p>
          <Link href="/app/restaurant/new">
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Your First Restaurant
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
