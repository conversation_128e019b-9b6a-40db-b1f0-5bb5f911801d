'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShoppingBag, Utensils, Wrench, Monitor, ShoppingCart } from 'lucide-react';

// Define types for shop categories
interface ShopCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  icon: React.ReactNode;
  href: string;
}

interface ShopCategoriesByType {
  [key: string]: ShopCategory[];
}

// Mock data for shop categories by type
const mockShopCategories: ShopCategoriesByType = {
  restaurant: [
    {
      id: 'restaurant-1',
      name: 'Restaurants',
      description: 'Manage your restaurant, menu items, tables, and reservations',
      image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2070&auto=format&fit=crop',
      icon: <Utensils className="h-5 w-5" />,
      href: '/app/restaurant'
    },
    {
      id: 'restaurant-2',
      name: 'Cafes',
      description: 'Manage your cafe, coffee menu, and quick service items',
      image: 'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?q=80&w=1978&auto=format&fit=crop',
      icon: <Utensils className="h-5 w-5" />,
      href: '/app/restaurant/cafe'
    },
    {
      id: 'restaurant-3',
      name: 'Bakeries',
      description: 'Manage your bakery products, orders, and inventory',
      image: 'https://images.unsplash.com/photo-1517433670267-08bbd4be890f?q=80&w=2080&auto=format&fit=crop',
      icon: <Utensils className="h-5 w-5" />,
      href: '/app/restaurant/bakery'
    },
    {
      id: 'restaurant-4',
      name: 'Food Trucks',
      description: 'Manage your mobile food service business',
      image: 'https://images.unsplash.com/photo-1565123409695-7b5ef63a2efb?q=80&w=2071&auto=format&fit=crop',
      icon: <Utensils className="h-5 w-5" />,
      href: '/app/restaurant/food-truck'
    }
  ],
  retail: [
    {
      id: 'retail-1',
      name: 'Clothing & Apparel',
      description: 'Manage your clothing store, inventory, and sales',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingBag className="h-5 w-5" />,
      href: '/app/retail/clothing'
    },
    {
      id: 'retail-2',
      name: 'Electronics',
      description: 'Manage your electronics store and product inventory',
      image: 'https://images.unsplash.com/photo-1550009158-9ebf69173e03?q=80&w=2101&auto=format&fit=crop',
      icon: <ShoppingBag className="h-5 w-5" />,
      href: '/app/retail/electronics'
    },
    {
      id: 'retail-3',
      name: 'Home & Furniture',
      description: 'Manage your home goods and furniture inventory',
      image: 'https://images.unsplash.com/photo-1538688525198-9b88f6f53126?q=80&w=2074&auto=format&fit=crop',
      icon: <ShoppingBag className="h-5 w-5" />,
      href: '/app/retail/home'
    },
    {
      id: 'retail-4',
      name: 'Beauty & Cosmetics',
      description: 'Manage your beauty products and cosmetics inventory',
      image: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?q=80&w=2080&auto=format&fit=crop',
      icon: <ShoppingBag className="h-5 w-5" />,
      href: '/app/retail/beauty'
    }
  ],
  service: [
    {
      id: 'service-1',
      name: 'Salons & Spas',
      description: 'Manage your salon services, appointments, and staff',
      image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop',
      icon: <Wrench className="h-5 w-5" />,
      href: '/app/service/salon'
    },
    {
      id: 'service-2',
      name: 'Fitness & Wellness',
      description: 'Manage your fitness center, classes, and memberships',
      image: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48?q=80&w=2070&auto=format&fit=crop',
      icon: <Wrench className="h-5 w-5" />,
      href: '/app/service/fitness'
    },
    {
      id: 'service-3',
      name: 'Auto Services',
      description: 'Manage your auto repair shop, services, and appointments',
      image: 'https://images.unsplash.com/photo-*************-78e95787c783?q=80&w=2070&auto=format&fit=crop',
      icon: <Wrench className="h-5 w-5" />,
      href: '/app/service/auto'
    },
    {
      id: 'service-4',
      name: 'Professional Services',
      description: 'Manage your consulting, legal, or accounting services',
      image: 'https://images.unsplash.com/photo-*************-c3d57bc86b40?q=80&w=2070&auto=format&fit=crop',
      icon: <Wrench className="h-5 w-5" />,
      href: '/app/service/professional'
    }
  ],
  digital: [
    {
      id: 'digital-1',
      name: 'Digital Products',
      description: 'Manage your digital downloads, courses, and content',
      image: 'https://images.unsplash.com/photo-*************-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop',
      icon: <Monitor className="h-5 w-5" />,
      href: '/app/digital/products'
    },
    {
      id: 'digital-2',
      name: 'Software & Apps',
      description: 'Manage your software licenses and app subscriptions',
      image: 'https://images.unsplash.com/photo-**********-bebda4e38f71?q=80&w=2070&auto=format&fit=crop',
      icon: <Monitor className="h-5 w-5" />,
      href: '/app/digital/software'
    },
    {
      id: 'digital-3',
      name: 'Online Courses',
      description: 'Manage your educational content and student access',
      image: 'https://images.unsplash.com/photo-1501504905252-473c47e087f8?q=80&w=2074&auto=format&fit=crop',
      icon: <Monitor className="h-5 w-5" />,
      href: '/app/digital/courses'
    },
    {
      id: 'digital-4',
      name: 'Subscription Services',
      description: 'Manage your recurring subscription-based products',
      image: 'https://images.unsplash.com/photo-1586892478025-2b5472316f22?q=80&w=2069&auto=format&fit=crop',
      icon: <Monitor className="h-5 w-5" />,
      href: '/app/digital/subscriptions'
    }
  ],
  convenience: [
    {
      id: 'convenience-1',
      name: 'Convenience Stores',
      description: 'Manage your convenience store inventory and sales',
      image: 'https://images.unsplash.com/photo-1515706886582-54c73c5eaf41?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingCart className="h-5 w-5" />,
      href: '/app/convenience/store'
    },
    {
      id: 'convenience-2',
      name: 'Mini Marts',
      description: 'Manage your mini mart products and inventory',
      image: 'https://images.unsplash.com/photo-1604719312566-8912e9c8a213?q=80&w=2069&auto=format&fit=crop',
      icon: <ShoppingCart className="h-5 w-5" />,
      href: '/app/convenience/mini-mart'
    },
    {
      id: 'convenience-3',
      name: 'Kiosks',
      description: 'Manage your kiosk products and quick service items',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingCart className="h-5 w-5" />,
      href: '/app/convenience/kiosk'
    },
    {
      id: 'convenience-4',
      name: 'Gas Stations',
      description: 'Manage your gas station convenience store',
      image: 'https://images.unsplash.com/photo-1545558014-8692077e9b5c?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingCart className="h-5 w-5" />,
      href: '/app/convenience/gas-station'
    }
  ]
};

export default function AppPage() {
  const [activeTab, setActiveTab] = useState('restaurant');

  return (
    <div className="font-be-vietnam container mx-auto">
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] text-[32px] font-bold leading-tight">Shop Categories</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Browse and manage your shops by category.</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="restaurant"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Restaurant</p>
            </TabsTrigger>
            <TabsTrigger
              value="retail"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Retail</p>
            </TabsTrigger>
            <TabsTrigger
              value="service"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Service</p>
            </TabsTrigger>
            <TabsTrigger
              value="digital"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Digital</p>
            </TabsTrigger>
            <TabsTrigger
              value="convenience"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Convenience</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {Object.keys(mockShopCategories).map((type) => (
          <TabsContent key={type} value={type} className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
              {mockShopCategories[type].map((category) => (
                <Link href={category.href} key={category.id}>
                  <Card className="h-full pt-0 overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                    <div
                      className="h-40 bg-cover bg-center"
                      style={{ backgroundImage: `url(${category.image})` }}
                    />
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-[#8a745c]">{category.icon}</span>
                        <CardTitle className="text-[#181510]">{category.name}</CardTitle>
                      </div>
                      <CardDescription className="text-[#8a745c]">{category.description}</CardDescription>
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
