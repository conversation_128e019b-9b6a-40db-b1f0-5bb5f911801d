import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON>_Vietnam_Pro } from "next/font/google";
import { notFound } from 'next/navigation';
import { getMessages } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';
import { locales, defaultLocale } from '@/i18n/config';
import ClientProviders from './ClientProviders';

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const beVietnamPro = Be_Vietnam_Pro({
  variable: "--font-be-vietnam-pro",
  subsets: ["latin"],
  weight: ["400", "500", "700", "900"],
});

export function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

export async function generateMetadata({ params }: { params: { locale: string } }) {
  return {
    title: 'ADC Shop Merchants',
    description: 'ADC Shop Merchants Portal',
  };
}

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // In Next.js 15, we need to await the params to avoid the error
  const { locale } = await params;

  // Validate that the locale is supported
  if (!locales.includes(locale as any)) {
    return notFound();
  }

  // Load messages for the locale
  let messages;
  try {
    messages = await getMessages({ locale });
  } catch (error) {
    console.error(`Failed to load messages for ${locale}:`, error);

    // Fallback to default locale
    try {
      messages = await getMessages({ locale: defaultLocale });
    } catch (fallbackError) {
      console.error(`Failed to load fallback messages:`, fallbackError);
      messages = {}; // Empty messages as last resort
    }
  }

  const fontClasses = `${geistSans.variable} ${geistMono.variable} ${beVietnamPro.variable} antialiased`;

  return (
    <html lang={locale}>
      <head>
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
      </head>
      <body className={fontClasses}>
        <ClientProviders locale={locale} messages={messages}>
          {children}
        </ClientProviders>
      </body>
    </html>
  );
}
