'use client';

import { NextIntlClientProvider } from 'next-intl';
import { ToastProvider } from '@/components/providers/ToastProvider';
import Providers from '@/components/providers/Providers';
import { Toaster } from '@/components/ui/sonner';
import { useEffect, useState } from 'react';

interface ProvidersProps {
  children: React.ReactNode;
  locale: string;
  messages: any;
}

export default function LocaleProviders({ children, locale, messages }: ProvidersProps) {
  const [mounted, setMounted] = useState(false);

  // Ensure hydration completes before rendering
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Bangkok">
      <Providers>
        <ToastProvider />
        <Toaster />
        {children}
      </Providers>
    </NextIntlClientProvider>
  );
}
