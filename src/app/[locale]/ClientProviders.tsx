'use client';

import { NextIntlClientProvider } from 'next-intl';
import { PropsWithChildren } from 'react';
import { Toaster } from 'sonner';
import { Provider } from 'react-redux';
import { store } from '@/lib/redux/store';
import { AppearanceProvider } from '@/lib/context/AppearanceContext';

type ClientProvidersProps = PropsWithChildren<{
  locale: string;
  messages: Record<string, any>;
}>;

export default function ClientProviders({
  children,
  locale,
  messages
}: ClientProvidersProps) {
  return (
    <Provider store={store}>
      <AppearanceProvider>
        <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Bangkok">
          {children}
          <Toaster position="top-right" richColors />
        </NextIntlClientProvider>
      </AppearanceProvider>
    </Provider>
  );
}
