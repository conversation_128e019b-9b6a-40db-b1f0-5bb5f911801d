import { NextRequest, NextResponse } from 'next/server';
import { reservationService } from '@/services/reservationService';
import { createReservationSchema } from '@/lib/validations/reservationSchema';
import { checkAuth } from '@/lib/auth';

/**
 * GET handler for fetching reservations
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get reservations for the merchant
    const reservations = await reservationService.getReservations(merchantId);
    
    return NextResponse.json(reservations);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/reservations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler for creating a reservation
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Get the merchant ID from the URL
    const { merchantId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = createReservationSchema.safeParse({
      ...body,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Create the reservation
    const reservation = await reservationService.createReservation(merchantId, body);
    
    return NextResponse.json(reservation, { status: 201 });
  } catch (error) {
    console.error('Error in POST /api/merchants/[merchantId]/reservations:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
