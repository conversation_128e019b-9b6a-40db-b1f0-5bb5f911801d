import { NextRequest, NextResponse } from 'next/server';
import { campaignSegmentService } from '@/services';
import { z } from 'zod';
import { supabase } from '@/lib/supabase/client';

// Schema for campaign segment filter
const campaignSegmentFilterSchema = z.object({
  field: z.string(),
  operator: z.enum([
    'equals',
    'not_equals',
    'contains',
    'not_contains',
    'greater_than',
    'less_than',
    'in',
    'not_in',
  ]),
  value: z.any(),
});

// Schema for creating a campaign segment
const createCampaignSegmentSchema = z.object({
  name: z.string().min(1, 'Segment name is required'),
  description: z.string().optional(),
  filters: z.array(campaignSegmentFilterSchema).min(1, 'At least one filter is required'),
});

/**
 * GET /api/merchants/[merchantId]/campaign-segments
 * Get all campaign segments for a merchant
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get segments
    const segments = await campaignSegmentService.getSegments(params.merchantId);

    return NextResponse.json(segments);
  } catch (error) {
    console.error('Error fetching campaign segments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch campaign segments' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/merchants/[merchantId]/campaign-segments
 * Create a new campaign segment
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { merchantId: string } }
) {
  try {
    // Verify authentication
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createCampaignSegmentSchema.parse(body);

    // Create segment
    const segment = await campaignSegmentService.createSegment({
      ...validatedData,
      merchantId: params.merchantId,
    });

    return NextResponse.json(segment, { status: 201 });
  } catch (error) {
    console.error('Error creating campaign segment:', error);
    if (error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }
    return NextResponse.json(
      { error: 'Failed to create campaign segment' },
      { status: 500 }
    );
  }
}
