import { NextRequest, NextResponse } from 'next/server';
import { appointmentService } from '@/services/appointmentService';
import { appointmentSchema } from '@/lib/validations/appointmentSchema';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 */
async function checkAuth(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
}

/**
 * GET handler for fetching a specific appointment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get the appointment
    const appointment = await appointmentService.getAppointmentById(merchantId, appointmentId);
    
    return NextResponse.json(appointment);
  } catch (error) {
    console.error('Error in GET /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a specific appointment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Parse and validate the request body
    const body = await request.json();
    
    // Validate the request body
    const validationResult = appointmentSchema.partial().safeParse({
      ...body,
      merchantId,
    });
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data', details: validationResult.error.format() },
        { status: 400 }
      );
    }
    
    // Update the appointment
    const appointment = await appointmentService.updateAppointment(merchantId, appointmentId, body);
    
    return NextResponse.json(appointment);
  } catch (error) {
    console.error('Error in PUT /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for cancelling a specific appointment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { merchantId: string; appointmentId: string } }
) {
  try {
    // Get the merchant ID and appointment ID from the URL
    const { merchantId, appointmentId } = params;
    
    // Check authentication
    const { authenticated } = await checkAuth(request);
    if (!authenticated) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Cancel the appointment
    const appointment = await appointmentService.cancelAppointment(merchantId, appointmentId);
    
    return NextResponse.json(appointment);
  } catch (error) {
    console.error('Error in DELETE /api/merchants/[merchantId]/appointments/[appointmentId]:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An error occurred' },
      { status: 500 }
    );
  }
}
