'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  accentColor: string;
  fontSize: string;
  reducedMotion: boolean;
  reducedTransparency: boolean;
  highContrast: boolean;
  compactMode: boolean;
  customFont: string;
}

interface AppearanceContextType {
  settings: AppearanceSettings;
  updateSettings: (newSettings: Partial<AppearanceSettings>) => void;
  applySettings: () => void;
}

const defaultSettings: AppearanceSettings = {
  theme: 'light',
  accentColor: 'earth',
  fontSize: 'medium',
  reducedMotion: false,
  reducedTransparency: false,
  highContrast: false,
  compactMode: false,
  customFont: 'be-vietnam',
};

// Color theme options
const colorThemes = {
  earth: { primary: '#8a745c', secondary: '#e5ccb2', bg: '#fbfaf9', text: '#181510', border: '#e5e1dc' },
  ocean: { primary: '#3b82f6', secondary: '#93c5fd', bg: '#f0f9ff', text: '#1e3a8a', border: '#bfdbfe' },
  forest: { primary: '#059669', secondary: '#a7f3d0', bg: '#ecfdf5', text: '#064e3b', border: '#86efac' },
  sunset: { primary: '#ea580c', secondary: '#fdba74', bg: '#fff7ed', text: '#7c2d12', border: '#fed7aa' },
  berry: { primary: '#8b5cf6', secondary: '#c4b5fd', bg: '#f5f3ff', text: '#4c1d95', border: '#ddd6fe' },
};

// Font size options
const fontSizeMap = {
  small: 0.875,
  medium: 1,
  large: 1.125,
  'x-large': 1.25,
};

// Font family options
const fontFamilyMap = {
  'be-vietnam': 'Be Vietnam Pro, sans-serif',
  inter: 'Inter, sans-serif',
  roboto: 'Roboto, sans-serif',
  poppins: 'Poppins, sans-serif',
  montserrat: 'Montserrat, sans-serif',
};

const AppearanceContext = createContext<AppearanceContextType | undefined>(undefined);

export function AppearanceProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<AppearanceSettings>(defaultSettings);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('appearance-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      } catch (error) {
        console.error('Failed to parse appearance settings:', error);
      }
    }
  }, []);

  // Apply settings to the document
  const applySettings = () => {
    const root = document.documentElement;
    const colorTheme = colorThemes[settings.accentColor as keyof typeof colorThemes] || colorThemes.earth;
    const fontSizeScale = fontSizeMap[settings.fontSize as keyof typeof fontSizeMap] || fontSizeMap.medium;
    const fontFamily = fontFamilyMap[settings.customFont as keyof typeof fontFamilyMap] || fontFamilyMap['be-vietnam'];

    // Apply theme
    if (settings.theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Apply color theme
    root.style.setProperty('--color-primary', colorTheme.primary);
    root.style.setProperty('--color-secondary', colorTheme.secondary);
    root.style.setProperty('--color-background', colorTheme.bg);
    root.style.setProperty('--color-text', colorTheme.text);
    root.style.setProperty('--color-border', colorTheme.border);

    // Apply font settings
    root.style.setProperty('--font-size-scale', fontSizeScale.toString());
    root.style.setProperty('--font-family', fontFamily);

    // Apply accessibility settings
    if (settings.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
    } else {
      root.style.setProperty('--animation-duration', '0.3s');
      root.style.setProperty('--transition-duration', '0.3s');
    }

    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    if (settings.compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }

    // Apply font size
    root.style.fontSize = `${fontSizeScale}rem`;
    root.style.fontFamily = fontFamily;
  };

  // Apply settings whenever they change
  useEffect(() => {
    applySettings();
  }, [settings]);

  const updateSettings = (newSettings: Partial<AppearanceSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    
    // Save to localStorage
    localStorage.setItem('appearance-settings', JSON.stringify(updatedSettings));
  };

  return (
    <AppearanceContext.Provider value={{ settings, updateSettings, applySettings }}>
      {children}
    </AppearanceContext.Provider>
  );
}

export function useAppearance() {
  const context = useContext(AppearanceContext);
  if (context === undefined) {
    throw new Error('useAppearance must be used within an AppearanceProvider');
  }
  return context;
}
