import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase/client';

// Base query with auth token
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',
  prepareHeaders: async (headers) => {
    // Get the session from Supabase
    const { data: { session } } = await supabase.auth.getSession();

    // If we have a session, add the token to the headers
    if (session) {
      headers.set('authorization', `Bearer ${session.access_token}`);
    }

    return headers;
  },
});

// Create the API slice
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: ['Merchants', 'Items', 'Orders', 'Users', 'Settings', 'Communications', 'Appointments', 'Campaigns', 'CampaignSegments', 'CommunicationAnalytics', 'Reviews', 'Reservations', 'Tables', 'Shops', 'Branches'],
  endpoints: () => ({}),
});
