import { apiSlice } from '../apiSlice';
import { Shop, Branch, ShopWithBranches, BranchWithShop } from '@/lib/types/shop';

// Define types for the new endpoints
interface CreateShopRequest {
  name: string;
  description: string;
  slug: string;
  logo?: string;
  coverImage?: string;
  address?: string;
  phoneNumber?: string;
  email?: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  cuisineType?: string;
  priceRange?: string;
}

interface CreateBranchRequest {
  name: string;
  description?: string;
  slug: string;
  address: string;
  phoneNumber?: string;
  email?: string;
  isMainBranch?: boolean;
  settings?: {
    seatingCapacity?: number;
    hasParking?: boolean;
    hasOutdoorSeating?: boolean;
    hasPrivateRooms?: boolean;
    openingHours?: Record<string, { open: string; close: string }>;
    reservationEnabled?: boolean;
    deliveryEnabled?: boolean;
    takeoutEnabled?: boolean;
  };
}

// Create the shop API
export const shopApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all shops
    getShops: builder.query<Shop[], void>({
      query: () => '/shops',
      providesTags: ['Shops'],
    }),

    // Get shop by ID
    getShopById: builder.query<Shop, string>({
      query: (id) => `/shops/${id}`,
      providesTags: (result, error, id) => [{ type: 'Shops', id }],
    }),

    // Get shop by slug
    getShopBySlug: builder.query<Shop, string>({
      query: (slug) => `/shops/slug/${slug}`,
      providesTags: (result, error, slug) => [{ type: 'Shops', id: result?.id }],
    }),

    // Get shops by owner
    getShopsByOwner: builder.query<Shop[], string>({
      query: (ownerId) => `/shops/owner/${ownerId}`,
      providesTags: ['Shops'],
    }),

    // Get shops by type
    getShopsByType: builder.query<Shop[], string>({
      query: (type) => `/shops/type/${type}`,
      providesTags: ['Shops'],
    }),

    // Create shop
    createShop: builder.mutation<Shop, CreateShopRequest>({
      query: (shop) => ({
        url: '/shops',
        method: 'POST',
        body: shop,
      }),
      invalidatesTags: ['Shops'],
    }),

    // Update shop
    updateShop: builder.mutation<Shop, { id: string; data: Partial<Shop> }>({
      query: ({ id, data }) => ({
        url: `/shops/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Shops', id }],
    }),

    // Delete shop
    deleteShop: builder.mutation<void, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Shops'],
    }),

    // Get all branches for a shop
    getBranches: builder.query<Branch[], string>({
      query: (shopId) => `/shops/${shopId}/branches`,
      providesTags: ['Branches'],
    }),

    // Get branch by ID
    getBranchById: builder.query<Branch, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}`,
      providesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Get branch by slug
    getBranchBySlug: builder.query<Branch, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => `/shops/slug/${shopSlug}/branches/slug/${branchSlug}`,
      providesTags: (result, error, { branchSlug }) => [{ type: 'Branches', id: result?.id }],
    }),

    // Create branch
    createBranch: builder.mutation<Branch, CreateBranchRequest & { shopId: string }>({
      query: ({ shopId, ...branch }) => ({
        url: `/shops/${shopId}/branches`,
        method: 'POST',
        body: branch,
      }),
      invalidatesTags: ['Branches'],
    }),

    // Update branch
    updateBranch: builder.mutation<Branch, { shopId: string; branchId: string; data: Partial<Branch> }>({
      query: ({ shopId, branchId, data }) => ({
        url: `/shops/${shopId}/branches/${branchId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Delete branch
    deleteBranch: builder.mutation<void, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Branches'],
    }),

    // Get shops with branches
    getShopsWithBranches: builder.query<ShopWithBranches[], void>({
      query: () => '/shops/with-branches',
      providesTags: ['Shops', 'Branches'],
    }),

    // Get branch with shop
    getBranchWithShop: builder.query<BranchWithShop, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/with-shop`,
      providesTags: (result, error, { branchSlug }) => [
        { type: 'Branches', id: result?.branch?.id },
        { type: 'Shops', id: result?.shop?.id },
      ],
    }),

    // Get branch settings
    getBranchSettings: builder.query<Branch['settings'], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}/settings`,
      providesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Update branch settings
    updateBranchSettings: builder.mutation<Branch, {
      shopId: string;
      branchId: string;
      settings: Branch['settings']
    }>({
      query: ({ shopId, branchId, settings }) => ({
        url: `/shops/${shopId}/branches/${branchId}/settings`,
        method: 'PUT',
        body: { settings },
      }),
      invalidatesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetShopsQuery,
  useGetShopByIdQuery,
  useGetShopBySlugQuery,
  useGetShopsByOwnerQuery,
  useGetShopsByTypeQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useDeleteShopMutation,
  useGetBranchesQuery,
  useGetBranchByIdQuery,
  useGetBranchBySlugQuery,
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
  useGetShopsWithBranchesQuery,
  useGetBranchWithShopQuery,
  useGetBranchSettingsQuery,
  useUpdateBranchSettingsMutation,
} = shopApi;
