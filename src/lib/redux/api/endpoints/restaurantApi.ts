import { apiSlice } from '../apiSlice';

// Restaurant-specific types
export interface RestaurantSettings {
  cuisineType: string;
  priceRange: string;
  seatingCapacity: number;
  reservationEnabled: boolean;
  deliveryEnabled: boolean;
  takeoutEnabled: boolean;
  openingHours: Record<string, { open: string; close: string }>;
}

export interface MenuItem {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  available: boolean;
  preparationTime: number;
  ingredients: string[];
  allergens: string[];
  nutritionalInfo: Record<string, any>;
}

export interface Table {
  id: string;
  merchantId: string;
  number: number;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'unavailable';
  location: string;
}

export interface Reservation {
  id: string;
  merchantId: string;
  userId: string;
  tableId: string;
  date: string;
  time: string;
  duration: number;
  partySize: number;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  specialRequests: string;
}

export interface Reviewer {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
}

export interface ReviewedItem {
  id: string;
  type: 'menu_item' | 'service' | 'ambiance' | 'other';
  name: string;
}

export interface Review {
  id: string;
  merchantId: string;
  reviewer: Reviewer;
  rating: number;
  date: string;
  content: string;
  reviewedItems: ReviewedItem[];
  status: 'published' | 'pending' | 'flagged' | 'archived';
  response?: string;
}

// Create the restaurant API
export const restaurantApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Menu Items
    getMenuItems: builder.query<MenuItem[], string>({
      query: (merchantId) => `/merchants/${merchantId}/menu-items`,
      providesTags: ['Items'],
    }),

    createMenuItem: builder.mutation<MenuItem, Partial<MenuItem> & { merchantId: string }>({
      query: ({ merchantId, ...item }) => ({
        url: `/merchants/${merchantId}/menu-items`,
        method: 'POST',
        body: item,
      }),
      invalidatesTags: ['Items'],
    }),

    updateMenuItem: builder.mutation<MenuItem, { merchantId: string; itemId: string; data: Partial<MenuItem> }>({
      query: ({ merchantId, itemId, data }) => ({
        url: `/merchants/${merchantId}/menu-items/${itemId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Items'],
    }),

    // Tables
    getTables: builder.query<Table[], string>({
      query: (merchantId) => `/merchants/${merchantId}/tables`,
      providesTags: ['Tables'],
    }),

    getTableById: builder.query<Table, { merchantId: string; tableId: string }>({
      query: ({ merchantId, tableId }) => `/merchants/${merchantId}/tables/${tableId}`,
      providesTags: (result, error, { tableId }) => [{ type: 'Tables', id: tableId }],
    }),

    createTable: builder.mutation<Table, Partial<Table> & { merchantId: string }>({
      query: ({ merchantId, ...table }) => ({
        url: `/merchants/${merchantId}/tables`,
        method: 'POST',
        body: table,
      }),
      invalidatesTags: ['Tables'],
    }),

    updateTable: builder.mutation<Table, { merchantId: string; tableId: string; data: Partial<Table> }>({
      query: ({ merchantId, tableId, data }) => ({
        url: `/merchants/${merchantId}/tables/${tableId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Tables'],
    }),

    deleteTable: builder.mutation<void, { merchantId: string; tableId: string }>({
      query: ({ merchantId, tableId }) => ({
        url: `/merchants/${merchantId}/tables/${tableId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Tables'],
    }),

    // Table Availability
    getTableAvailability: builder.query<any[], { merchantId: string; date: string; partySize: number }>({
      query: ({ merchantId, date, partySize }) =>
        `/merchants/${merchantId}/tables/availability?date=${date}&partySize=${partySize}`,
      providesTags: ['Tables', 'Reservations'],
    }),

    // Reservations
    getReservations: builder.query<Reservation[], string>({
      query: (merchantId) => `/merchants/${merchantId}/reservations`,
      providesTags: ['Reservations'],
    }),

    getReservationById: builder.query<Reservation, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => `/merchants/${merchantId}/reservations/${reservationId}`,
      providesTags: (result, error, { reservationId }) => [{ type: 'Reservations', id: reservationId }],
    }),

    createReservation: builder.mutation<Reservation, Partial<Reservation> & { merchantId: string }>({
      query: ({ merchantId, ...reservation }) => ({
        url: `/merchants/${merchantId}/reservations`,
        method: 'POST',
        body: reservation,
      }),
      invalidatesTags: ['Reservations'],
    }),

    updateReservation: builder.mutation<Reservation, { merchantId: string; reservationId: string; data: Partial<Reservation> }>({
      query: ({ merchantId, reservationId, data }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Reservations'],
    }),

    deleteReservation: builder.mutation<void, { merchantId: string; reservationId: string }>({
      query: ({ merchantId, reservationId }) => ({
        url: `/merchants/${merchantId}/reservations/${reservationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Reservations'],
    }),

    // Reviews
    getReviews: builder.query<Review[], { merchantId: string; status?: string }>({
      query: ({ merchantId, status }) => {
        let url = `/merchants/${merchantId}/reviews`;
        if (status) {
          url += `?status=${status}`;
        }
        return url;
      },
      providesTags: ['Reviews'],
    }),

    getReviewById: builder.query<Review, { merchantId: string; reviewId: string }>({
      query: ({ merchantId, reviewId }) => `/merchants/${merchantId}/reviews/${reviewId}`,
      providesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    respondToReview: builder.mutation<Review, { merchantId: string; reviewId: string; response: string }>({
      query: ({ merchantId, reviewId, response }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { response },
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),

    updateReviewStatus: builder.mutation<Review, { merchantId: string; reviewId: string; status: string }>({
      query: ({ merchantId, reviewId, status }) => ({
        url: `/merchants/${merchantId}/reviews/${reviewId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { reviewId }) => [{ type: 'Reviews', id: reviewId }],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetMenuItemsQuery,
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation,
  useGetTablesQuery,
  useGetTableByIdQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useGetTableAvailabilityQuery,
  useGetReservationsQuery,
  useGetReservationByIdQuery,
  useCreateReservationMutation,
  useUpdateReservationMutation,
  useDeleteReservationMutation,
  useGetReviewsQuery,
  useGetReviewByIdQuery,
  useRespondToReviewMutation,
  useUpdateReviewStatusMutation,
} = restaurantApi;
