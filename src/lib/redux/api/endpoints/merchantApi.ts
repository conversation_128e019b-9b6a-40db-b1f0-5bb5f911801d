import { apiSlice } from '../apiSlice';

// Define types
export interface Merchant {
  id: string;
  name: string;
  type: 'restaurant' | 'retail' | 'service' | 'digital' | 'custom' | 'convenience';
  status: string;
  ownerId: string;
  // Common fields for all merchant types
  address: string;
  phoneNumber: string;
  email: string;
  logo: string;
  // Type-specific fields will be in the settings object
  settings: Record<string, any>;
}

// Create the merchants API
export const merchantApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all merchants
    getMerchants: builder.query<Merchant[], void>({
      query: () => '/merchants',
      providesTags: ['Merchants'],
    }),

    // Get merchant by ID
    getMerchant: builder.query<Merchant, string>({
      query: (id) => `/merchants/${id}`,
      providesTags: (result, error, id) => [{ type: 'Merchants', id }],
    }),

    // Get merchants by type
    getMerchantsByType: builder.query<Merchant[], string>({
      query: (type) => `/merchants?type=${type}`,
      providesTags: ['Merchants'],
    }),

    // Create merchant
    createMerchant: builder.mutation<Merchant, Partial<Merchant>>({
      query: (merchant) => ({
        url: '/merchants',
        method: 'POST',
        body: merchant,
      }),
      invalidatesTags: ['Merchants'],
    }),

    // Update merchant
    updateMerchant: builder.mutation<Merchant, { id: string; data: Partial<Merchant> }>({
      query: ({ id, data }) => ({
        url: `/merchants/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Merchants', id }],
    }),

    // Delete merchant
    deleteMerchant: builder.mutation<void, string>({
      query: (id) => ({
        url: `/merchants/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Merchants'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetMerchantsQuery,
  useGetMerchantQuery,
  useGetMerchantsByTypeQuery,
  useCreateMerchantMutation,
  useUpdateMerchantMutation,
  useDeleteMerchantMutation,
} = merchantApi;
