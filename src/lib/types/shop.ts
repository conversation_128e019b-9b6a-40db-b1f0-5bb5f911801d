/**
 * Shop and Branch types for the application
 */

// Shop type
export interface Shop {
  id: string;
  slug: string;
  name: string;
  description: string;
  logo: string;
  type: 'restaurant' | 'retail' | 'service' | 'digital' | 'custom' | 'convenience';
  ownerId: string;
  status: 'active' | 'pending' | 'inactive';
  createdAt: string;
  updatedAt: string;
  branches: Branch[];
  // Type-specific settings
  settings: Record<string, any>;
}

// Branch type
export interface Branch {
  id: string;
  slug: string;
  shopId: string;
  name: string;
  description: string;
  address: string;
  phoneNumber: string;
  email: string;
  status: 'active' | 'pending' | 'inactive';
  isMainBranch: boolean;
  createdAt: string;
  updatedAt: string;
  // Branch-specific settings
  settings: Record<string, any>;
}

// Shop with branches type
export interface ShopWithBranches extends Shop {
  branches: Branch[];
}

// Branch with shop type
export interface BranchWithShop extends Branch {
  shop: Shop;
}
