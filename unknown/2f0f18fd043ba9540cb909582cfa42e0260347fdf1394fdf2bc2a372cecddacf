'use client';

import { NavigationProvider } from '@/lib/context/NavigationContext';
import { usePathname } from '@/i18n/navigation';
import NotificationPopover from '@/components/notifications/NotificationPopover';
import ProfileMenu from '@/components/navigation/ProfileMenu';

// Simple layout for restaurant pages
export default function RestaurantLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Check if we're in a branch page
  // Pattern: /app/restaurant/[slugShop]/[slugBranch]
  const isBranchPage = pathname.split('/').filter(Boolean).length > 3;

  return (
    <NavigationProvider>
      <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] font-be-vietnam overflow-x-hidden">
        {/* Only show the header if we're not in a branch page */}
        {!isBranchPage && (
          <div className="sticky top-0 z-10 w-full border-b border-[#e2dcd4] bg-[#fbfaf9]">
            <div className="flex h-16 items-center justify-between px-4 md:px-6 lg:px-8">
              <div className="flex items-center">
                <div className="text-[#181510] font-medium">
                  Restaurant Management
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <NotificationPopover />
                <ProfileMenu />
              </div>
            </div>
          </div>
        )}
        <div className="flex-1">
          <div className="px-4 md:px-6 lg:px-8 py-6">
            <div className="layout-content-container max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </div>
      </div>
    </NavigationProvider>
  );
}
